# CDN

Recommended: [jsDelivr](//cdn.jsdelivr.net), which will reflect the latest version as soon as it is published to npm. You can also browse the source of the npm package at [cdn.jsdelivr.net/npm/docsify/](//cdn.jsdelivr.net/npm/docsify/).

## Latest version

```html
<!-- load css -->
<link rel="stylesheet" href="//cdn.jsdelivr.net/npm/docsify/themes/vue.css">

<!-- load script -->
<script src="//cdn.jsdelivr.net/npm/docsify/lib/docsify.js"></script>
```

Alternatively, use [compressed files](#compressed-file).

## Specific version

```html
<!-- load css -->
<link rel="stylesheet" href="//cdn.jsdelivr.net/npm/docsify@4.10.2/themes/vue.css">

<!-- load script -->
<script src="//cdn.jsdelivr.net/npm/docsify@4.10.2/lib/docsify.js"></script>
```

## Compressed file

```html
<!-- load css -->
<link rel="stylesheet" href="//cdn.jsdelivr.net/npm/docsify/lib/themes/vue.css">

<!-- load script -->
<script src="//cdn.jsdelivr.net/npm/docsify/lib/docsify.min.js"></script>
```

```html
<!-- load css -->
<link rel="stylesheet" href="//cdn.jsdelivr.net/npm/docsify@4.10.2/lib/themes/vue.css">

<!-- load script -->
<script src="//cdn.jsdelivr.net/npm/docsify@4.10.2/lib/docsify.min.js"></script>
```

## Other CDN

- https://www.bootcdn.cn/docsify/
- https://cdn.jsdelivr.net/npm/docsify/
- https://cdnjs.com/libraries/docsify
- https://unpkg.com/browse/docsify/
