# Emoji

Below is a complete list of emoji shorthand codes. Docsify can be configured to render emoji using GitHub-style emoji images or native emoji characters using the [`nativeEmoji`](configuration#nativeemoji) configuration option.

<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(15em, 1fr));">

<!-- START: Auto-generated content (/build/emoji.js) -->

:100: `:100:`

:1234: `:1234:`

:+1: `:+1:`

:-1: `:-1:`

:1st_place_medal: `:1st_place_medal:`

:2nd_place_medal: `:2nd_place_medal:`

:3rd_place_medal: `:3rd_place_medal:`

:8ball: `:8ball:`

:a: `:a:`

:ab: `:ab:`

:abacus: `:abacus:`

:abc: `:abc:`

:abcd: `:abcd:`

:accept: `:accept:`

:accessibility: `:accessibility:`

:accordion: `:accordion:`

:adhesive_bandage: `:adhesive_bandage:`

:adult: `:adult:`

:aerial_tramway: `:aerial_tramway:`

:afghanistan: `:afghanistan:`

:airplane: `:airplane:`

:aland_islands: `:aland_islands:`

:alarm_clock: `:alarm_clock:`

:albania: `:albania:`

:alembic: `:alembic:`

:algeria: `:algeria:`

:alien: `:alien:`

:ambulance: `:ambulance:`

:american_samoa: `:american_samoa:`

:amphora: `:amphora:`

:anatomical_heart: `:anatomical_heart:`

:anchor: `:anchor:`

:andorra: `:andorra:`

:angel: `:angel:`

:anger: `:anger:`

:angola: `:angola:`

:angry: `:angry:`

:anguilla: `:anguilla:`

:anguished: `:anguished:`

:ant: `:ant:`

:antarctica: `:antarctica:`

:antigua_barbuda: `:antigua_barbuda:`

:apple: `:apple:`

:aquarius: `:aquarius:`

:argentina: `:argentina:`

:aries: `:aries:`

:armenia: `:armenia:`

:arrow_backward: `:arrow_backward:`

:arrow_double_down: `:arrow_double_down:`

:arrow_double_up: `:arrow_double_up:`

:arrow_down: `:arrow_down:`

:arrow_down_small: `:arrow_down_small:`

:arrow_forward: `:arrow_forward:`

:arrow_heading_down: `:arrow_heading_down:`

:arrow_heading_up: `:arrow_heading_up:`

:arrow_left: `:arrow_left:`

:arrow_lower_left: `:arrow_lower_left:`

:arrow_lower_right: `:arrow_lower_right:`

:arrow_right: `:arrow_right:`

:arrow_right_hook: `:arrow_right_hook:`

:arrow_up: `:arrow_up:`

:arrow_up_down: `:arrow_up_down:`

:arrow_up_small: `:arrow_up_small:`

:arrow_upper_left: `:arrow_upper_left:`

:arrow_upper_right: `:arrow_upper_right:`

:arrows_clockwise: `:arrows_clockwise:`

:arrows_counterclockwise: `:arrows_counterclockwise:`

:art: `:art:`

:articulated_lorry: `:articulated_lorry:`

:artificial_satellite: `:artificial_satellite:`

:artist: `:artist:`

:aruba: `:aruba:`

:ascension_island: `:ascension_island:`

:asterisk: `:asterisk:`

:astonished: `:astonished:`

:astronaut: `:astronaut:`

:athletic_shoe: `:athletic_shoe:`

:atm: `:atm:`

:atom: `:atom:`

:atom_symbol: `:atom_symbol:`

:australia: `:australia:`

:austria: `:austria:`

:auto_rickshaw: `:auto_rickshaw:`

:avocado: `:avocado:`

:axe: `:axe:`

:azerbaijan: `:azerbaijan:`

:b: `:b:`

:baby: `:baby:`

:baby_bottle: `:baby_bottle:`

:baby_chick: `:baby_chick:`

:baby_symbol: `:baby_symbol:`

:back: `:back:`

:bacon: `:bacon:`

:badger: `:badger:`

:badminton: `:badminton:`

:bagel: `:bagel:`

:baggage_claim: `:baggage_claim:`

:baguette_bread: `:baguette_bread:`

:bahamas: `:bahamas:`

:bahrain: `:bahrain:`

:balance_scale: `:balance_scale:`

:bald_man: `:bald_man:`

:bald_woman: `:bald_woman:`

:ballet_shoes: `:ballet_shoes:`

:balloon: `:balloon:`

:ballot_box: `:ballot_box:`

:ballot_box_with_check: `:ballot_box_with_check:`

:bamboo: `:bamboo:`

:banana: `:banana:`

:bangbang: `:bangbang:`

:bangladesh: `:bangladesh:`

:banjo: `:banjo:`

:bank: `:bank:`

:bar_chart: `:bar_chart:`

:barbados: `:barbados:`

:barber: `:barber:`

:baseball: `:baseball:`

:basecamp: `:basecamp:`

:basecampy: `:basecampy:`

:basket: `:basket:`

:basketball: `:basketball:`

:basketball_man: `:basketball_man:`

:basketball_woman: `:basketball_woman:`

:bat: `:bat:`

:bath: `:bath:`

:bathtub: `:bathtub:`

:battery: `:battery:`

:beach_umbrella: `:beach_umbrella:`

:beans: `:beans:`

:bear: `:bear:`

:bearded_person: `:bearded_person:`

:beaver: `:beaver:`

:bed: `:bed:`

:bee: `:bee:`

:beer: `:beer:`

:beers: `:beers:`

:beetle: `:beetle:`

:beginner: `:beginner:`

:belarus: `:belarus:`

:belgium: `:belgium:`

:belize: `:belize:`

:bell: `:bell:`

:bell_pepper: `:bell_pepper:`

:bellhop_bell: `:bellhop_bell:`

:benin: `:benin:`

:bento: `:bento:`

:bermuda: `:bermuda:`

:beverage_box: `:beverage_box:`

:bhutan: `:bhutan:`

:bicyclist: `:bicyclist:`

:bike: `:bike:`

:biking_man: `:biking_man:`

:biking_woman: `:biking_woman:`

:bikini: `:bikini:`

:billed_cap: `:billed_cap:`

:biohazard: `:biohazard:`

:bird: `:bird:`

:birthday: `:birthday:`

:bison: `:bison:`

:biting_lip: `:biting_lip:`

:black_bird: `:black_bird:`

:black_cat: `:black_cat:`

:black_circle: `:black_circle:`

:black_flag: `:black_flag:`

:black_heart: `:black_heart:`

:black_joker: `:black_joker:`

:black_large_square: `:black_large_square:`

:black_medium_small_square: `:black_medium_small_square:`

:black_medium_square: `:black_medium_square:`

:black_nib: `:black_nib:`

:black_small_square: `:black_small_square:`

:black_square_button: `:black_square_button:`

:blond_haired_man: `:blond_haired_man:`

:blond_haired_person: `:blond_haired_person:`

:blond_haired_woman: `:blond_haired_woman:`

:blonde_woman: `:blonde_woman:`

:blossom: `:blossom:`

:blowfish: `:blowfish:`

:blue_book: `:blue_book:`

:blue_car: `:blue_car:`

:blue_heart: `:blue_heart:`

:blue_square: `:blue_square:`

:blueberries: `:blueberries:`

:blush: `:blush:`

:boar: `:boar:`

:boat: `:boat:`

:bolivia: `:bolivia:`

:bomb: `:bomb:`

:bone: `:bone:`

:book: `:book:`

:bookmark: `:bookmark:`

:bookmark_tabs: `:bookmark_tabs:`

:books: `:books:`

:boom: `:boom:`

:boomerang: `:boomerang:`

:boot: `:boot:`

:bosnia_herzegovina: `:bosnia_herzegovina:`

:botswana: `:botswana:`

:bouncing_ball_man: `:bouncing_ball_man:`

:bouncing_ball_person: `:bouncing_ball_person:`

:bouncing_ball_woman: `:bouncing_ball_woman:`

:bouquet: `:bouquet:`

:bouvet_island: `:bouvet_island:`

:bow: `:bow:`

:bow_and_arrow: `:bow_and_arrow:`

:bowing_man: `:bowing_man:`

:bowing_woman: `:bowing_woman:`

:bowl_with_spoon: `:bowl_with_spoon:`

:bowling: `:bowling:`

:bowtie: `:bowtie:`

:boxing_glove: `:boxing_glove:`

:boy: `:boy:`

:brain: `:brain:`

:brazil: `:brazil:`

:bread: `:bread:`

:breast_feeding: `:breast_feeding:`

:bricks: `:bricks:`

:bride_with_veil: `:bride_with_veil:`

:bridge_at_night: `:bridge_at_night:`

:briefcase: `:briefcase:`

:british_indian_ocean_territory: `:british_indian_ocean_territory:`

:british_virgin_islands: `:british_virgin_islands:`

:broccoli: `:broccoli:`

:broken_heart: `:broken_heart:`

:broom: `:broom:`

:brown_circle: `:brown_circle:`

:brown_heart: `:brown_heart:`

:brown_square: `:brown_square:`

:brunei: `:brunei:`

:bubble_tea: `:bubble_tea:`

:bubbles: `:bubbles:`

:bucket: `:bucket:`

:bug: `:bug:`

:building_construction: `:building_construction:`

:bulb: `:bulb:`

:bulgaria: `:bulgaria:`

:bullettrain_front: `:bullettrain_front:`

:bullettrain_side: `:bullettrain_side:`

:burkina_faso: `:burkina_faso:`

:burrito: `:burrito:`

:burundi: `:burundi:`

:bus: `:bus:`

:business_suit_levitating: `:business_suit_levitating:`

:busstop: `:busstop:`

:bust_in_silhouette: `:bust_in_silhouette:`

:busts_in_silhouette: `:busts_in_silhouette:`

:butter: `:butter:`

:butterfly: `:butterfly:`

:cactus: `:cactus:`

:cake: `:cake:`

:calendar: `:calendar:`

:call_me_hand: `:call_me_hand:`

:calling: `:calling:`

:cambodia: `:cambodia:`

:camel: `:camel:`

:camera: `:camera:`

:camera_flash: `:camera_flash:`

:cameroon: `:cameroon:`

:camping: `:camping:`

:canada: `:canada:`

:canary_islands: `:canary_islands:`

:cancer: `:cancer:`

:candle: `:candle:`

:candy: `:candy:`

:canned_food: `:canned_food:`

:canoe: `:canoe:`

:cape_verde: `:cape_verde:`

:capital_abcd: `:capital_abcd:`

:capricorn: `:capricorn:`

:car: `:car:`

:card_file_box: `:card_file_box:`

:card_index: `:card_index:`

:card_index_dividers: `:card_index_dividers:`

:caribbean_netherlands: `:caribbean_netherlands:`

:carousel_horse: `:carousel_horse:`

:carpentry_saw: `:carpentry_saw:`

:carrot: `:carrot:`

:cartwheeling: `:cartwheeling:`

:cat: `:cat:`

:cat2: `:cat2:`

:cayman_islands: `:cayman_islands:`

:cd: `:cd:`

:central_african_republic: `:central_african_republic:`

:ceuta_melilla: `:ceuta_melilla:`

:chad: `:chad:`

:chains: `:chains:`

:chair: `:chair:`

:champagne: `:champagne:`

:chart: `:chart:`

:chart_with_downwards_trend: `:chart_with_downwards_trend:`

:chart_with_upwards_trend: `:chart_with_upwards_trend:`

:checkered_flag: `:checkered_flag:`

:cheese: `:cheese:`

:cherries: `:cherries:`

:cherry_blossom: `:cherry_blossom:`

:chess_pawn: `:chess_pawn:`

:chestnut: `:chestnut:`

:chicken: `:chicken:`

:child: `:child:`

:children_crossing: `:children_crossing:`

:chile: `:chile:`

:chipmunk: `:chipmunk:`

:chocolate_bar: `:chocolate_bar:`

:chopsticks: `:chopsticks:`

:christmas_island: `:christmas_island:`

:christmas_tree: `:christmas_tree:`

:church: `:church:`

:cinema: `:cinema:`

:circus_tent: `:circus_tent:`

:city_sunrise: `:city_sunrise:`

:city_sunset: `:city_sunset:`

:cityscape: `:cityscape:`

:cl: `:cl:`

:clamp: `:clamp:`

:clap: `:clap:`

:clapper: `:clapper:`

:classical_building: `:classical_building:`

:climbing: `:climbing:`

:climbing_man: `:climbing_man:`

:climbing_woman: `:climbing_woman:`

:clinking_glasses: `:clinking_glasses:`

:clipboard: `:clipboard:`

:clipperton_island: `:clipperton_island:`

:clock1: `:clock1:`

:clock10: `:clock10:`

:clock1030: `:clock1030:`

:clock11: `:clock11:`

:clock1130: `:clock1130:`

:clock12: `:clock12:`

:clock1230: `:clock1230:`

:clock130: `:clock130:`

:clock2: `:clock2:`

:clock230: `:clock230:`

:clock3: `:clock3:`

:clock330: `:clock330:`

:clock4: `:clock4:`

:clock430: `:clock430:`

:clock5: `:clock5:`

:clock530: `:clock530:`

:clock6: `:clock6:`

:clock630: `:clock630:`

:clock7: `:clock7:`

:clock730: `:clock730:`

:clock8: `:clock8:`

:clock830: `:clock830:`

:clock9: `:clock9:`

:clock930: `:clock930:`

:closed_book: `:closed_book:`

:closed_lock_with_key: `:closed_lock_with_key:`

:closed_umbrella: `:closed_umbrella:`

:cloud: `:cloud:`

:cloud_with_lightning: `:cloud_with_lightning:`

:cloud_with_lightning_and_rain: `:cloud_with_lightning_and_rain:`

:cloud_with_rain: `:cloud_with_rain:`

:cloud_with_snow: `:cloud_with_snow:`

:clown_face: `:clown_face:`

:clubs: `:clubs:`

:cn: `:cn:`

:coat: `:coat:`

:cockroach: `:cockroach:`

:cocktail: `:cocktail:`

:coconut: `:coconut:`

:cocos_islands: `:cocos_islands:`

:coffee: `:coffee:`

:coffin: `:coffin:`

:coin: `:coin:`

:cold_face: `:cold_face:`

:cold_sweat: `:cold_sweat:`

:collision: `:collision:`

:colombia: `:colombia:`

:comet: `:comet:`

:comoros: `:comoros:`

:compass: `:compass:`

:computer: `:computer:`

:computer_mouse: `:computer_mouse:`

:confetti_ball: `:confetti_ball:`

:confounded: `:confounded:`

:confused: `:confused:`

:congo_brazzaville: `:congo_brazzaville:`

:congo_kinshasa: `:congo_kinshasa:`

:congratulations: `:congratulations:`

:construction: `:construction:`

:construction_worker: `:construction_worker:`

:construction_worker_man: `:construction_worker_man:`

:construction_worker_woman: `:construction_worker_woman:`

:control_knobs: `:control_knobs:`

:convenience_store: `:convenience_store:`

:cook: `:cook:`

:cook_islands: `:cook_islands:`

:cookie: `:cookie:`

:cool: `:cool:`

:cop: `:cop:`

:copyright: `:copyright:`

:coral: `:coral:`

:corn: `:corn:`

:costa_rica: `:costa_rica:`

:cote_divoire: `:cote_divoire:`

:couch_and_lamp: `:couch_and_lamp:`

:couple: `:couple:`

:couple_with_heart: `:couple_with_heart:`

:couple_with_heart_man_man: `:couple_with_heart_man_man:`

:couple_with_heart_woman_man: `:couple_with_heart_woman_man:`

:couple_with_heart_woman_woman: `:couple_with_heart_woman_woman:`

:couplekiss: `:couplekiss:`

:couplekiss_man_man: `:couplekiss_man_man:`

:couplekiss_man_woman: `:couplekiss_man_woman:`

:couplekiss_woman_woman: `:couplekiss_woman_woman:`

:cow: `:cow:`

:cow2: `:cow2:`

:cowboy_hat_face: `:cowboy_hat_face:`

:crab: `:crab:`

:crayon: `:crayon:`

:credit_card: `:credit_card:`

:crescent_moon: `:crescent_moon:`

:cricket: `:cricket:`

:cricket_game: `:cricket_game:`

:croatia: `:croatia:`

:crocodile: `:crocodile:`

:croissant: `:croissant:`

:crossed_fingers: `:crossed_fingers:`

:crossed_flags: `:crossed_flags:`

:crossed_swords: `:crossed_swords:`

:crown: `:crown:`

:crutch: `:crutch:`

:cry: `:cry:`

:crying_cat_face: `:crying_cat_face:`

:crystal_ball: `:crystal_ball:`

:cuba: `:cuba:`

:cucumber: `:cucumber:`

:cup_with_straw: `:cup_with_straw:`

:cupcake: `:cupcake:`

:cupid: `:cupid:`

:curacao: `:curacao:`

:curling_stone: `:curling_stone:`

:curly_haired_man: `:curly_haired_man:`

:curly_haired_woman: `:curly_haired_woman:`

:curly_loop: `:curly_loop:`

:currency_exchange: `:currency_exchange:`

:curry: `:curry:`

:cursing_face: `:cursing_face:`

:custard: `:custard:`

:customs: `:customs:`

:cut_of_meat: `:cut_of_meat:`

:cyclone: `:cyclone:`

:cyprus: `:cyprus:`

:czech_republic: `:czech_republic:`

:dagger: `:dagger:`

:dancer: `:dancer:`

:dancers: `:dancers:`

:dancing_men: `:dancing_men:`

:dancing_women: `:dancing_women:`

:dango: `:dango:`

:dark_sunglasses: `:dark_sunglasses:`

:dart: `:dart:`

:dash: `:dash:`

:date: `:date:`

:de: `:de:`

:deaf_man: `:deaf_man:`

:deaf_person: `:deaf_person:`

:deaf_woman: `:deaf_woman:`

:deciduous_tree: `:deciduous_tree:`

:deer: `:deer:`

:denmark: `:denmark:`

:department_store: `:department_store:`

:dependabot: `:dependabot:`

:derelict_house: `:derelict_house:`

:desert: `:desert:`

:desert_island: `:desert_island:`

:desktop_computer: `:desktop_computer:`

:detective: `:detective:`

:diamond_shape_with_a_dot_inside: `:diamond_shape_with_a_dot_inside:`

:diamonds: `:diamonds:`

:diego_garcia: `:diego_garcia:`

:disappointed: `:disappointed:`

:disappointed_relieved: `:disappointed_relieved:`

:disguised_face: `:disguised_face:`

:diving_mask: `:diving_mask:`

:diya_lamp: `:diya_lamp:`

:dizzy: `:dizzy:`

:dizzy_face: `:dizzy_face:`

:djibouti: `:djibouti:`

:dna: `:dna:`

:do_not_litter: `:do_not_litter:`

:dodo: `:dodo:`

:dog: `:dog:`

:dog2: `:dog2:`

:dollar: `:dollar:`

:dolls: `:dolls:`

:dolphin: `:dolphin:`

:dominica: `:dominica:`

:dominican_republic: `:dominican_republic:`

:donkey: `:donkey:`

:door: `:door:`

:dotted_line_face: `:dotted_line_face:`

:doughnut: `:doughnut:`

:dove: `:dove:`

:dragon: `:dragon:`

:dragon_face: `:dragon_face:`

:dress: `:dress:`

:dromedary_camel: `:dromedary_camel:`

:drooling_face: `:drooling_face:`

:drop_of_blood: `:drop_of_blood:`

:droplet: `:droplet:`

:drum: `:drum:`

:duck: `:duck:`

:dumpling: `:dumpling:`

:dvd: `:dvd:`

:e-mail: `:e-mail:`

:eagle: `:eagle:`

:ear: `:ear:`

:ear_of_rice: `:ear_of_rice:`

:ear_with_hearing_aid: `:ear_with_hearing_aid:`

:earth_africa: `:earth_africa:`

:earth_americas: `:earth_americas:`

:earth_asia: `:earth_asia:`

:ecuador: `:ecuador:`

:egg: `:egg:`

:eggplant: `:eggplant:`

:egypt: `:egypt:`

:eight: `:eight:`

:eight_pointed_black_star: `:eight_pointed_black_star:`

:eight_spoked_asterisk: `:eight_spoked_asterisk:`

:eject_button: `:eject_button:`

:el_salvador: `:el_salvador:`

:electric_plug: `:electric_plug:`

:electron: `:electron:`

:elephant: `:elephant:`

:elevator: `:elevator:`

:elf: `:elf:`

:elf_man: `:elf_man:`

:elf_woman: `:elf_woman:`

:email: `:email:`

:empty_nest: `:empty_nest:`

:end: `:end:`

:england: `:england:`

:envelope: `:envelope:`

:envelope_with_arrow: `:envelope_with_arrow:`

:equatorial_guinea: `:equatorial_guinea:`

:eritrea: `:eritrea:`

:es: `:es:`

:estonia: `:estonia:`

:ethiopia: `:ethiopia:`

:eu: `:eu:`

:euro: `:euro:`

:european_castle: `:european_castle:`

:european_post_office: `:european_post_office:`

:european_union: `:european_union:`

:evergreen_tree: `:evergreen_tree:`

:exclamation: `:exclamation:`

:exploding_head: `:exploding_head:`

:expressionless: `:expressionless:`

:eye: `:eye:`

:eye_speech_bubble: `:eye_speech_bubble:`

:eyeglasses: `:eyeglasses:`

:eyes: `:eyes:`

:face_exhaling: `:face_exhaling:`

:face_holding_back_tears: `:face_holding_back_tears:`

:face_in_clouds: `:face_in_clouds:`

:face_with_diagonal_mouth: `:face_with_diagonal_mouth:`

:face_with_head_bandage: `:face_with_head_bandage:`

:face_with_open_eyes_and_hand_over_mouth: `:face_with_open_eyes_and_hand_over_mouth:`

:face_with_peeking_eye: `:face_with_peeking_eye:`

:face_with_spiral_eyes: `:face_with_spiral_eyes:`

:face_with_thermometer: `:face_with_thermometer:`

:facepalm: `:facepalm:`

:facepunch: `:facepunch:`

:factory: `:factory:`

:factory_worker: `:factory_worker:`

:fairy: `:fairy:`

:fairy_man: `:fairy_man:`

:fairy_woman: `:fairy_woman:`

:falafel: `:falafel:`

:falkland_islands: `:falkland_islands:`

:fallen_leaf: `:fallen_leaf:`

:family: `:family:`

:family_man_boy: `:family_man_boy:`

:family_man_boy_boy: `:family_man_boy_boy:`

:family_man_girl: `:family_man_girl:`

:family_man_girl_boy: `:family_man_girl_boy:`

:family_man_girl_girl: `:family_man_girl_girl:`

:family_man_man_boy: `:family_man_man_boy:`

:family_man_man_boy_boy: `:family_man_man_boy_boy:`

:family_man_man_girl: `:family_man_man_girl:`

:family_man_man_girl_boy: `:family_man_man_girl_boy:`

:family_man_man_girl_girl: `:family_man_man_girl_girl:`

:family_man_woman_boy: `:family_man_woman_boy:`

:family_man_woman_boy_boy: `:family_man_woman_boy_boy:`

:family_man_woman_girl: `:family_man_woman_girl:`

:family_man_woman_girl_boy: `:family_man_woman_girl_boy:`

:family_man_woman_girl_girl: `:family_man_woman_girl_girl:`

:family_woman_boy: `:family_woman_boy:`

:family_woman_boy_boy: `:family_woman_boy_boy:`

:family_woman_girl: `:family_woman_girl:`

:family_woman_girl_boy: `:family_woman_girl_boy:`

:family_woman_girl_girl: `:family_woman_girl_girl:`

:family_woman_woman_boy: `:family_woman_woman_boy:`

:family_woman_woman_boy_boy: `:family_woman_woman_boy_boy:`

:family_woman_woman_girl: `:family_woman_woman_girl:`

:family_woman_woman_girl_boy: `:family_woman_woman_girl_boy:`

:family_woman_woman_girl_girl: `:family_woman_woman_girl_girl:`

:farmer: `:farmer:`

:faroe_islands: `:faroe_islands:`

:fast_forward: `:fast_forward:`

:fax: `:fax:`

:fearful: `:fearful:`

:feather: `:feather:`

:feelsgood: `:feelsgood:`

:feet: `:feet:`

:female_detective: `:female_detective:`

:female_sign: `:female_sign:`

:ferris_wheel: `:ferris_wheel:`

:ferry: `:ferry:`

:field_hockey: `:field_hockey:`

:fiji: `:fiji:`

:file_cabinet: `:file_cabinet:`

:file_folder: `:file_folder:`

:film_projector: `:film_projector:`

:film_strip: `:film_strip:`

:finland: `:finland:`

:finnadie: `:finnadie:`

:fire: `:fire:`

:fire_engine: `:fire_engine:`

:fire_extinguisher: `:fire_extinguisher:`

:firecracker: `:firecracker:`

:firefighter: `:firefighter:`

:fireworks: `:fireworks:`

:first_quarter_moon: `:first_quarter_moon:`

:first_quarter_moon_with_face: `:first_quarter_moon_with_face:`

:fish: `:fish:`

:fish_cake: `:fish_cake:`

:fishing_pole_and_fish: `:fishing_pole_and_fish:`

:fishsticks: `:fishsticks:`

:fist: `:fist:`

:fist_left: `:fist_left:`

:fist_oncoming: `:fist_oncoming:`

:fist_raised: `:fist_raised:`

:fist_right: `:fist_right:`

:five: `:five:`

:flags: `:flags:`

:flamingo: `:flamingo:`

:flashlight: `:flashlight:`

:flat_shoe: `:flat_shoe:`

:flatbread: `:flatbread:`

:fleur_de_lis: `:fleur_de_lis:`

:flight_arrival: `:flight_arrival:`

:flight_departure: `:flight_departure:`

:flipper: `:flipper:`

:floppy_disk: `:floppy_disk:`

:flower_playing_cards: `:flower_playing_cards:`

:flushed: `:flushed:`

:flute: `:flute:`

:fly: `:fly:`

:flying_disc: `:flying_disc:`

:flying_saucer: `:flying_saucer:`

:fog: `:fog:`

:foggy: `:foggy:`

:folding_hand_fan: `:folding_hand_fan:`

:fondue: `:fondue:`

:foot: `:foot:`

:football: `:football:`

:footprints: `:footprints:`

:fork_and_knife: `:fork_and_knife:`

:fortune_cookie: `:fortune_cookie:`

:fountain: `:fountain:`

:fountain_pen: `:fountain_pen:`

:four: `:four:`

:four_leaf_clover: `:four_leaf_clover:`

:fox_face: `:fox_face:`

:fr: `:fr:`

:framed_picture: `:framed_picture:`

:free: `:free:`

:french_guiana: `:french_guiana:`

:french_polynesia: `:french_polynesia:`

:french_southern_territories: `:french_southern_territories:`

:fried_egg: `:fried_egg:`

:fried_shrimp: `:fried_shrimp:`

:fries: `:fries:`

:frog: `:frog:`

:frowning: `:frowning:`

:frowning_face: `:frowning_face:`

:frowning_man: `:frowning_man:`

:frowning_person: `:frowning_person:`

:frowning_woman: `:frowning_woman:`

:fu: `:fu:`

:fuelpump: `:fuelpump:`

:full_moon: `:full_moon:`

:full_moon_with_face: `:full_moon_with_face:`

:funeral_urn: `:funeral_urn:`

:gabon: `:gabon:`

:gambia: `:gambia:`

:game_die: `:game_die:`

:garlic: `:garlic:`

:gb: `:gb:`

:gear: `:gear:`

:gem: `:gem:`

:gemini: `:gemini:`

:genie: `:genie:`

:genie_man: `:genie_man:`

:genie_woman: `:genie_woman:`

:georgia: `:georgia:`

:ghana: `:ghana:`

:ghost: `:ghost:`

:gibraltar: `:gibraltar:`

:gift: `:gift:`

:gift_heart: `:gift_heart:`

:ginger_root: `:ginger_root:`

:giraffe: `:giraffe:`

:girl: `:girl:`

:globe_with_meridians: `:globe_with_meridians:`

:gloves: `:gloves:`

:goal_net: `:goal_net:`

:goat: `:goat:`

:goberserk: `:goberserk:`

:godmode: `:godmode:`

:goggles: `:goggles:`

:golf: `:golf:`

:golfing: `:golfing:`

:golfing_man: `:golfing_man:`

:golfing_woman: `:golfing_woman:`

:goose: `:goose:`

:gorilla: `:gorilla:`

:grapes: `:grapes:`

:greece: `:greece:`

:green_apple: `:green_apple:`

:green_book: `:green_book:`

:green_circle: `:green_circle:`

:green_heart: `:green_heart:`

:green_salad: `:green_salad:`

:green_square: `:green_square:`

:greenland: `:greenland:`

:grenada: `:grenada:`

:grey_exclamation: `:grey_exclamation:`

:grey_heart: `:grey_heart:`

:grey_question: `:grey_question:`

:grimacing: `:grimacing:`

:grin: `:grin:`

:grinning: `:grinning:`

:guadeloupe: `:guadeloupe:`

:guam: `:guam:`

:guard: `:guard:`

:guardsman: `:guardsman:`

:guardswoman: `:guardswoman:`

:guatemala: `:guatemala:`

:guernsey: `:guernsey:`

:guide_dog: `:guide_dog:`

:guinea: `:guinea:`

:guinea_bissau: `:guinea_bissau:`

:guitar: `:guitar:`

:gun: `:gun:`

:guyana: `:guyana:`

:hair_pick: `:hair_pick:`

:haircut: `:haircut:`

:haircut_man: `:haircut_man:`

:haircut_woman: `:haircut_woman:`

:haiti: `:haiti:`

:hamburger: `:hamburger:`

:hammer: `:hammer:`

:hammer_and_pick: `:hammer_and_pick:`

:hammer_and_wrench: `:hammer_and_wrench:`

:hamsa: `:hamsa:`

:hamster: `:hamster:`

:hand: `:hand:`

:hand_over_mouth: `:hand_over_mouth:`

:hand_with_index_finger_and_thumb_crossed: `:hand_with_index_finger_and_thumb_crossed:`

:handbag: `:handbag:`

:handball_person: `:handball_person:`

:handshake: `:handshake:`

:hankey: `:hankey:`

:hash: `:hash:`

:hatched_chick: `:hatched_chick:`

:hatching_chick: `:hatching_chick:`

:headphones: `:headphones:`

:headstone: `:headstone:`

:health_worker: `:health_worker:`

:hear_no_evil: `:hear_no_evil:`

:heard_mcdonald_islands: `:heard_mcdonald_islands:`

:heart: `:heart:`

:heart_decoration: `:heart_decoration:`

:heart_eyes: `:heart_eyes:`

:heart_eyes_cat: `:heart_eyes_cat:`

:heart_hands: `:heart_hands:`

:heart_on_fire: `:heart_on_fire:`

:heartbeat: `:heartbeat:`

:heartpulse: `:heartpulse:`

:hearts: `:hearts:`

:heavy_check_mark: `:heavy_check_mark:`

:heavy_division_sign: `:heavy_division_sign:`

:heavy_dollar_sign: `:heavy_dollar_sign:`

:heavy_equals_sign: `:heavy_equals_sign:`

:heavy_exclamation_mark: `:heavy_exclamation_mark:`

:heavy_heart_exclamation: `:heavy_heart_exclamation:`

:heavy_minus_sign: `:heavy_minus_sign:`

:heavy_multiplication_x: `:heavy_multiplication_x:`

:heavy_plus_sign: `:heavy_plus_sign:`

:hedgehog: `:hedgehog:`

:helicopter: `:helicopter:`

:herb: `:herb:`

:hibiscus: `:hibiscus:`

:high_brightness: `:high_brightness:`

:high_heel: `:high_heel:`

:hiking_boot: `:hiking_boot:`

:hindu_temple: `:hindu_temple:`

:hippopotamus: `:hippopotamus:`

:hocho: `:hocho:`

:hole: `:hole:`

:honduras: `:honduras:`

:honey_pot: `:honey_pot:`

:honeybee: `:honeybee:`

:hong_kong: `:hong_kong:`

:hook: `:hook:`

:horse: `:horse:`

:horse_racing: `:horse_racing:`

:hospital: `:hospital:`

:hot_face: `:hot_face:`

:hot_pepper: `:hot_pepper:`

:hotdog: `:hotdog:`

:hotel: `:hotel:`

:hotsprings: `:hotsprings:`

:hourglass: `:hourglass:`

:hourglass_flowing_sand: `:hourglass_flowing_sand:`

:house: `:house:`

:house_with_garden: `:house_with_garden:`

:houses: `:houses:`

:hugs: `:hugs:`

:hungary: `:hungary:`

:hurtrealbad: `:hurtrealbad:`

:hushed: `:hushed:`

:hut: `:hut:`

:hyacinth: `:hyacinth:`

:ice_cream: `:ice_cream:`

:ice_cube: `:ice_cube:`

:ice_hockey: `:ice_hockey:`

:ice_skate: `:ice_skate:`

:icecream: `:icecream:`

:iceland: `:iceland:`

:id: `:id:`

:identification_card: `:identification_card:`

:ideograph_advantage: `:ideograph_advantage:`

:imp: `:imp:`

:inbox_tray: `:inbox_tray:`

:incoming_envelope: `:incoming_envelope:`

:index_pointing_at_the_viewer: `:index_pointing_at_the_viewer:`

:india: `:india:`

:indonesia: `:indonesia:`

:infinity: `:infinity:`

:information_desk_person: `:information_desk_person:`

:information_source: `:information_source:`

:innocent: `:innocent:`

:interrobang: `:interrobang:`

:iphone: `:iphone:`

:iran: `:iran:`

:iraq: `:iraq:`

:ireland: `:ireland:`

:isle_of_man: `:isle_of_man:`

:israel: `:israel:`

:it: `:it:`

:izakaya_lantern: `:izakaya_lantern:`

:jack_o_lantern: `:jack_o_lantern:`

:jamaica: `:jamaica:`

:japan: `:japan:`

:japanese_castle: `:japanese_castle:`

:japanese_goblin: `:japanese_goblin:`

:japanese_ogre: `:japanese_ogre:`

:jar: `:jar:`

:jeans: `:jeans:`

:jellyfish: `:jellyfish:`

:jersey: `:jersey:`

:jigsaw: `:jigsaw:`

:jordan: `:jordan:`

:joy: `:joy:`

:joy_cat: `:joy_cat:`

:joystick: `:joystick:`

:jp: `:jp:`

:judge: `:judge:`

:juggling_person: `:juggling_person:`

:kaaba: `:kaaba:`

:kangaroo: `:kangaroo:`

:kazakhstan: `:kazakhstan:`

:kenya: `:kenya:`

:key: `:key:`

:keyboard: `:keyboard:`

:keycap_ten: `:keycap_ten:`

:khanda: `:khanda:`

:kick_scooter: `:kick_scooter:`

:kimono: `:kimono:`

:kiribati: `:kiribati:`

:kiss: `:kiss:`

:kissing: `:kissing:`

:kissing_cat: `:kissing_cat:`

:kissing_closed_eyes: `:kissing_closed_eyes:`

:kissing_heart: `:kissing_heart:`

:kissing_smiling_eyes: `:kissing_smiling_eyes:`

:kite: `:kite:`

:kiwi_fruit: `:kiwi_fruit:`

:kneeling_man: `:kneeling_man:`

:kneeling_person: `:kneeling_person:`

:kneeling_woman: `:kneeling_woman:`

:knife: `:knife:`

:knot: `:knot:`

:koala: `:koala:`

:koko: `:koko:`

:kosovo: `:kosovo:`

:kr: `:kr:`

:kuwait: `:kuwait:`

:kyrgyzstan: `:kyrgyzstan:`

:lab_coat: `:lab_coat:`

:label: `:label:`

:lacrosse: `:lacrosse:`

:ladder: `:ladder:`

:lady_beetle: `:lady_beetle:`

:lantern: `:lantern:`

:laos: `:laos:`

:large_blue_circle: `:large_blue_circle:`

:large_blue_diamond: `:large_blue_diamond:`

:large_orange_diamond: `:large_orange_diamond:`

:last_quarter_moon: `:last_quarter_moon:`

:last_quarter_moon_with_face: `:last_quarter_moon_with_face:`

:latin_cross: `:latin_cross:`

:latvia: `:latvia:`

:laughing: `:laughing:`

:leafy_green: `:leafy_green:`

:leaves: `:leaves:`

:lebanon: `:lebanon:`

:ledger: `:ledger:`

:left_luggage: `:left_luggage:`

:left_right_arrow: `:left_right_arrow:`

:left_speech_bubble: `:left_speech_bubble:`

:leftwards_arrow_with_hook: `:leftwards_arrow_with_hook:`

:leftwards_hand: `:leftwards_hand:`

:leftwards_pushing_hand: `:leftwards_pushing_hand:`

:leg: `:leg:`

:lemon: `:lemon:`

:leo: `:leo:`

:leopard: `:leopard:`

:lesotho: `:lesotho:`

:level_slider: `:level_slider:`

:liberia: `:liberia:`

:libra: `:libra:`

:libya: `:libya:`

:liechtenstein: `:liechtenstein:`

:light_blue_heart: `:light_blue_heart:`

:light_rail: `:light_rail:`

:link: `:link:`

:lion: `:lion:`

:lips: `:lips:`

:lipstick: `:lipstick:`

:lithuania: `:lithuania:`

:lizard: `:lizard:`

:llama: `:llama:`

:lobster: `:lobster:`

:lock: `:lock:`

:lock_with_ink_pen: `:lock_with_ink_pen:`

:lollipop: `:lollipop:`

:long_drum: `:long_drum:`

:loop: `:loop:`

:lotion_bottle: `:lotion_bottle:`

:lotus: `:lotus:`

:lotus_position: `:lotus_position:`

:lotus_position_man: `:lotus_position_man:`

:lotus_position_woman: `:lotus_position_woman:`

:loud_sound: `:loud_sound:`

:loudspeaker: `:loudspeaker:`

:love_hotel: `:love_hotel:`

:love_letter: `:love_letter:`

:love_you_gesture: `:love_you_gesture:`

:low_battery: `:low_battery:`

:low_brightness: `:low_brightness:`

:luggage: `:luggage:`

:lungs: `:lungs:`

:luxembourg: `:luxembourg:`

:lying_face: `:lying_face:`

:m: `:m:`

:macau: `:macau:`

:macedonia: `:macedonia:`

:madagascar: `:madagascar:`

:mag: `:mag:`

:mag_right: `:mag_right:`

:mage: `:mage:`

:mage_man: `:mage_man:`

:mage_woman: `:mage_woman:`

:magic_wand: `:magic_wand:`

:magnet: `:magnet:`

:mahjong: `:mahjong:`

:mailbox: `:mailbox:`

:mailbox_closed: `:mailbox_closed:`

:mailbox_with_mail: `:mailbox_with_mail:`

:mailbox_with_no_mail: `:mailbox_with_no_mail:`

:malawi: `:malawi:`

:malaysia: `:malaysia:`

:maldives: `:maldives:`

:male_detective: `:male_detective:`

:male_sign: `:male_sign:`

:mali: `:mali:`

:malta: `:malta:`

:mammoth: `:mammoth:`

:man: `:man:`

:man_artist: `:man_artist:`

:man_astronaut: `:man_astronaut:`

:man_beard: `:man_beard:`

:man_cartwheeling: `:man_cartwheeling:`

:man_cook: `:man_cook:`

:man_dancing: `:man_dancing:`

:man_facepalming: `:man_facepalming:`

:man_factory_worker: `:man_factory_worker:`

:man_farmer: `:man_farmer:`

:man_feeding_baby: `:man_feeding_baby:`

:man_firefighter: `:man_firefighter:`

:man_health_worker: `:man_health_worker:`

:man_in_manual_wheelchair: `:man_in_manual_wheelchair:`

:man_in_motorized_wheelchair: `:man_in_motorized_wheelchair:`

:man_in_tuxedo: `:man_in_tuxedo:`

:man_judge: `:man_judge:`

:man_juggling: `:man_juggling:`

:man_mechanic: `:man_mechanic:`

:man_office_worker: `:man_office_worker:`

:man_pilot: `:man_pilot:`

:man_playing_handball: `:man_playing_handball:`

:man_playing_water_polo: `:man_playing_water_polo:`

:man_scientist: `:man_scientist:`

:man_shrugging: `:man_shrugging:`

:man_singer: `:man_singer:`

:man_student: `:man_student:`

:man_teacher: `:man_teacher:`

:man_technologist: `:man_technologist:`

:man_with_gua_pi_mao: `:man_with_gua_pi_mao:`

:man_with_probing_cane: `:man_with_probing_cane:`

:man_with_turban: `:man_with_turban:`

:man_with_veil: `:man_with_veil:`

:mandarin: `:mandarin:`

:mango: `:mango:`

:mans_shoe: `:mans_shoe:`

:mantelpiece_clock: `:mantelpiece_clock:`

:manual_wheelchair: `:manual_wheelchair:`

:maple_leaf: `:maple_leaf:`

:maracas: `:maracas:`

:marshall_islands: `:marshall_islands:`

:martial_arts_uniform: `:martial_arts_uniform:`

:martinique: `:martinique:`

:mask: `:mask:`

:massage: `:massage:`

:massage_man: `:massage_man:`

:massage_woman: `:massage_woman:`

:mate: `:mate:`

:mauritania: `:mauritania:`

:mauritius: `:mauritius:`

:mayotte: `:mayotte:`

:meat_on_bone: `:meat_on_bone:`

:mechanic: `:mechanic:`

:mechanical_arm: `:mechanical_arm:`

:mechanical_leg: `:mechanical_leg:`

:medal_military: `:medal_military:`

:medal_sports: `:medal_sports:`

:medical_symbol: `:medical_symbol:`

:mega: `:mega:`

:melon: `:melon:`

:melting_face: `:melting_face:`

:memo: `:memo:`

:men_wrestling: `:men_wrestling:`

:mending_heart: `:mending_heart:`

:menorah: `:menorah:`

:mens: `:mens:`

:mermaid: `:mermaid:`

:merman: `:merman:`

:merperson: `:merperson:`

:metal: `:metal:`

:metro: `:metro:`

:mexico: `:mexico:`

:microbe: `:microbe:`

:micronesia: `:micronesia:`

:microphone: `:microphone:`

:microscope: `:microscope:`

:middle_finger: `:middle_finger:`

:military_helmet: `:military_helmet:`

:milk_glass: `:milk_glass:`

:milky_way: `:milky_way:`

:minibus: `:minibus:`

:minidisc: `:minidisc:`

:mirror: `:mirror:`

:mirror_ball: `:mirror_ball:`

:mobile_phone_off: `:mobile_phone_off:`

:moldova: `:moldova:`

:monaco: `:monaco:`

:money_mouth_face: `:money_mouth_face:`

:money_with_wings: `:money_with_wings:`

:moneybag: `:moneybag:`

:mongolia: `:mongolia:`

:monkey: `:monkey:`

:monkey_face: `:monkey_face:`

:monocle_face: `:monocle_face:`

:monorail: `:monorail:`

:montenegro: `:montenegro:`

:montserrat: `:montserrat:`

:moon: `:moon:`

:moon_cake: `:moon_cake:`

:moose: `:moose:`

:morocco: `:morocco:`

:mortar_board: `:mortar_board:`

:mosque: `:mosque:`

:mosquito: `:mosquito:`

:motor_boat: `:motor_boat:`

:motor_scooter: `:motor_scooter:`

:motorcycle: `:motorcycle:`

:motorized_wheelchair: `:motorized_wheelchair:`

:motorway: `:motorway:`

:mount_fuji: `:mount_fuji:`

:mountain: `:mountain:`

:mountain_bicyclist: `:mountain_bicyclist:`

:mountain_biking_man: `:mountain_biking_man:`

:mountain_biking_woman: `:mountain_biking_woman:`

:mountain_cableway: `:mountain_cableway:`

:mountain_railway: `:mountain_railway:`

:mountain_snow: `:mountain_snow:`

:mouse: `:mouse:`

:mouse2: `:mouse2:`

:mouse_trap: `:mouse_trap:`

:movie_camera: `:movie_camera:`

:moyai: `:moyai:`

:mozambique: `:mozambique:`

:mrs_claus: `:mrs_claus:`

:muscle: `:muscle:`

:mushroom: `:mushroom:`

:musical_keyboard: `:musical_keyboard:`

:musical_note: `:musical_note:`

:musical_score: `:musical_score:`

:mute: `:mute:`

:mx_claus: `:mx_claus:`

:myanmar: `:myanmar:`

:nail_care: `:nail_care:`

:name_badge: `:name_badge:`

:namibia: `:namibia:`

:national_park: `:national_park:`

:nauru: `:nauru:`

:nauseated_face: `:nauseated_face:`

:nazar_amulet: `:nazar_amulet:`

:neckbeard: `:neckbeard:`

:necktie: `:necktie:`

:negative_squared_cross_mark: `:negative_squared_cross_mark:`

:nepal: `:nepal:`

:nerd_face: `:nerd_face:`

:nest_with_eggs: `:nest_with_eggs:`

:nesting_dolls: `:nesting_dolls:`

:netherlands: `:netherlands:`

:neutral_face: `:neutral_face:`

:new: `:new:`

:new_caledonia: `:new_caledonia:`

:new_moon: `:new_moon:`

:new_moon_with_face: `:new_moon_with_face:`

:new_zealand: `:new_zealand:`

:newspaper: `:newspaper:`

:newspaper_roll: `:newspaper_roll:`

:next_track_button: `:next_track_button:`

:ng: `:ng:`

:ng_man: `:ng_man:`

:ng_woman: `:ng_woman:`

:nicaragua: `:nicaragua:`

:niger: `:niger:`

:nigeria: `:nigeria:`

:night_with_stars: `:night_with_stars:`

:nine: `:nine:`

:ninja: `:ninja:`

:niue: `:niue:`

:no_bell: `:no_bell:`

:no_bicycles: `:no_bicycles:`

:no_entry: `:no_entry:`

:no_entry_sign: `:no_entry_sign:`

:no_good: `:no_good:`

:no_good_man: `:no_good_man:`

:no_good_woman: `:no_good_woman:`

:no_mobile_phones: `:no_mobile_phones:`

:no_mouth: `:no_mouth:`

:no_pedestrians: `:no_pedestrians:`

:no_smoking: `:no_smoking:`

:non-potable_water: `:non-potable_water:`

:norfolk_island: `:norfolk_island:`

:north_korea: `:north_korea:`

:northern_mariana_islands: `:northern_mariana_islands:`

:norway: `:norway:`

:nose: `:nose:`

:notebook: `:notebook:`

:notebook_with_decorative_cover: `:notebook_with_decorative_cover:`

:notes: `:notes:`

:nut_and_bolt: `:nut_and_bolt:`

:o: `:o:`

:o2: `:o2:`

:ocean: `:ocean:`

:octocat: `:octocat:`

:octopus: `:octopus:`

:oden: `:oden:`

:office: `:office:`

:office_worker: `:office_worker:`

:oil_drum: `:oil_drum:`

:ok: `:ok:`

:ok_hand: `:ok_hand:`

:ok_man: `:ok_man:`

:ok_person: `:ok_person:`

:ok_woman: `:ok_woman:`

:old_key: `:old_key:`

:older_adult: `:older_adult:`

:older_man: `:older_man:`

:older_woman: `:older_woman:`

:olive: `:olive:`

:om: `:om:`

:oman: `:oman:`

:on: `:on:`

:oncoming_automobile: `:oncoming_automobile:`

:oncoming_bus: `:oncoming_bus:`

:oncoming_police_car: `:oncoming_police_car:`

:oncoming_taxi: `:oncoming_taxi:`

:one: `:one:`

:one_piece_swimsuit: `:one_piece_swimsuit:`

:onion: `:onion:`

:open_book: `:open_book:`

:open_file_folder: `:open_file_folder:`

:open_hands: `:open_hands:`

:open_mouth: `:open_mouth:`

:open_umbrella: `:open_umbrella:`

:ophiuchus: `:ophiuchus:`

:orange: `:orange:`

:orange_book: `:orange_book:`

:orange_circle: `:orange_circle:`

:orange_heart: `:orange_heart:`

:orange_square: `:orange_square:`

:orangutan: `:orangutan:`

:orthodox_cross: `:orthodox_cross:`

:otter: `:otter:`

:outbox_tray: `:outbox_tray:`

:owl: `:owl:`

:ox: `:ox:`

:oyster: `:oyster:`

:package: `:package:`

:page_facing_up: `:page_facing_up:`

:page_with_curl: `:page_with_curl:`

:pager: `:pager:`

:paintbrush: `:paintbrush:`

:pakistan: `:pakistan:`

:palau: `:palau:`

:palestinian_territories: `:palestinian_territories:`

:palm_down_hand: `:palm_down_hand:`

:palm_tree: `:palm_tree:`

:palm_up_hand: `:palm_up_hand:`

:palms_up_together: `:palms_up_together:`

:panama: `:panama:`

:pancakes: `:pancakes:`

:panda_face: `:panda_face:`

:paperclip: `:paperclip:`

:paperclips: `:paperclips:`

:papua_new_guinea: `:papua_new_guinea:`

:parachute: `:parachute:`

:paraguay: `:paraguay:`

:parasol_on_ground: `:parasol_on_ground:`

:parking: `:parking:`

:parrot: `:parrot:`

:part_alternation_mark: `:part_alternation_mark:`

:partly_sunny: `:partly_sunny:`

:partying_face: `:partying_face:`

:passenger_ship: `:passenger_ship:`

:passport_control: `:passport_control:`

:pause_button: `:pause_button:`

:paw_prints: `:paw_prints:`

:pea_pod: `:pea_pod:`

:peace_symbol: `:peace_symbol:`

:peach: `:peach:`

:peacock: `:peacock:`

:peanuts: `:peanuts:`

:pear: `:pear:`

:pen: `:pen:`

:pencil: `:pencil:`

:pencil2: `:pencil2:`

:penguin: `:penguin:`

:pensive: `:pensive:`

:people_holding_hands: `:people_holding_hands:`

:people_hugging: `:people_hugging:`

:performing_arts: `:performing_arts:`

:persevere: `:persevere:`

:person_bald: `:person_bald:`

:person_curly_hair: `:person_curly_hair:`

:person_feeding_baby: `:person_feeding_baby:`

:person_fencing: `:person_fencing:`

:person_in_manual_wheelchair: `:person_in_manual_wheelchair:`

:person_in_motorized_wheelchair: `:person_in_motorized_wheelchair:`

:person_in_tuxedo: `:person_in_tuxedo:`

:person_red_hair: `:person_red_hair:`

:person_white_hair: `:person_white_hair:`

:person_with_crown: `:person_with_crown:`

:person_with_probing_cane: `:person_with_probing_cane:`

:person_with_turban: `:person_with_turban:`

:person_with_veil: `:person_with_veil:`

:peru: `:peru:`

:petri_dish: `:petri_dish:`

:philippines: `:philippines:`

:phone: `:phone:`

:pick: `:pick:`

:pickup_truck: `:pickup_truck:`

:pie: `:pie:`

:pig: `:pig:`

:pig2: `:pig2:`

:pig_nose: `:pig_nose:`

:pill: `:pill:`

:pilot: `:pilot:`

:pinata: `:pinata:`

:pinched_fingers: `:pinched_fingers:`

:pinching_hand: `:pinching_hand:`

:pineapple: `:pineapple:`

:ping_pong: `:ping_pong:`

:pink_heart: `:pink_heart:`

:pirate_flag: `:pirate_flag:`

:pisces: `:pisces:`

:pitcairn_islands: `:pitcairn_islands:`

:pizza: `:pizza:`

:placard: `:placard:`

:place_of_worship: `:place_of_worship:`

:plate_with_cutlery: `:plate_with_cutlery:`

:play_or_pause_button: `:play_or_pause_button:`

:playground_slide: `:playground_slide:`

:pleading_face: `:pleading_face:`

:plunger: `:plunger:`

:point_down: `:point_down:`

:point_left: `:point_left:`

:point_right: `:point_right:`

:point_up: `:point_up:`

:point_up_2: `:point_up_2:`

:poland: `:poland:`

:polar_bear: `:polar_bear:`

:police_car: `:police_car:`

:police_officer: `:police_officer:`

:policeman: `:policeman:`

:policewoman: `:policewoman:`

:poodle: `:poodle:`

:poop: `:poop:`

:popcorn: `:popcorn:`

:portugal: `:portugal:`

:post_office: `:post_office:`

:postal_horn: `:postal_horn:`

:postbox: `:postbox:`

:potable_water: `:potable_water:`

:potato: `:potato:`

:potted_plant: `:potted_plant:`

:pouch: `:pouch:`

:poultry_leg: `:poultry_leg:`

:pound: `:pound:`

:pouring_liquid: `:pouring_liquid:`

:pout: `:pout:`

:pouting_cat: `:pouting_cat:`

:pouting_face: `:pouting_face:`

:pouting_man: `:pouting_man:`

:pouting_woman: `:pouting_woman:`

:pray: `:pray:`

:prayer_beads: `:prayer_beads:`

:pregnant_man: `:pregnant_man:`

:pregnant_person: `:pregnant_person:`

:pregnant_woman: `:pregnant_woman:`

:pretzel: `:pretzel:`

:previous_track_button: `:previous_track_button:`

:prince: `:prince:`

:princess: `:princess:`

:printer: `:printer:`

:probing_cane: `:probing_cane:`

:puerto_rico: `:puerto_rico:`

:punch: `:punch:`

:purple_circle: `:purple_circle:`

:purple_heart: `:purple_heart:`

:purple_square: `:purple_square:`

:purse: `:purse:`

:pushpin: `:pushpin:`

:put_litter_in_its_place: `:put_litter_in_its_place:`

:qatar: `:qatar:`

:question: `:question:`

:rabbit: `:rabbit:`

:rabbit2: `:rabbit2:`

:raccoon: `:raccoon:`

:racehorse: `:racehorse:`

:racing_car: `:racing_car:`

:radio: `:radio:`

:radio_button: `:radio_button:`

:radioactive: `:radioactive:`

:rage: `:rage:`

:rage1: `:rage1:`

:rage2: `:rage2:`

:rage3: `:rage3:`

:rage4: `:rage4:`

:railway_car: `:railway_car:`

:railway_track: `:railway_track:`

:rainbow: `:rainbow:`

:rainbow_flag: `:rainbow_flag:`

:raised_back_of_hand: `:raised_back_of_hand:`

:raised_eyebrow: `:raised_eyebrow:`

:raised_hand: `:raised_hand:`

:raised_hand_with_fingers_splayed: `:raised_hand_with_fingers_splayed:`

:raised_hands: `:raised_hands:`

:raising_hand: `:raising_hand:`

:raising_hand_man: `:raising_hand_man:`

:raising_hand_woman: `:raising_hand_woman:`

:ram: `:ram:`

:ramen: `:ramen:`

:rat: `:rat:`

:razor: `:razor:`

:receipt: `:receipt:`

:record_button: `:record_button:`

:recycle: `:recycle:`

:red_car: `:red_car:`

:red_circle: `:red_circle:`

:red_envelope: `:red_envelope:`

:red_haired_man: `:red_haired_man:`

:red_haired_woman: `:red_haired_woman:`

:red_square: `:red_square:`

:registered: `:registered:`

:relaxed: `:relaxed:`

:relieved: `:relieved:`

:reminder_ribbon: `:reminder_ribbon:`

:repeat: `:repeat:`

:repeat_one: `:repeat_one:`

:rescue_worker_helmet: `:rescue_worker_helmet:`

:restroom: `:restroom:`

:reunion: `:reunion:`

:revolving_hearts: `:revolving_hearts:`

:rewind: `:rewind:`

:rhinoceros: `:rhinoceros:`

:ribbon: `:ribbon:`

:rice: `:rice:`

:rice_ball: `:rice_ball:`

:rice_cracker: `:rice_cracker:`

:rice_scene: `:rice_scene:`

:right_anger_bubble: `:right_anger_bubble:`

:rightwards_hand: `:rightwards_hand:`

:rightwards_pushing_hand: `:rightwards_pushing_hand:`

:ring: `:ring:`

:ring_buoy: `:ring_buoy:`

:ringed_planet: `:ringed_planet:`

:robot: `:robot:`

:rock: `:rock:`

:rocket: `:rocket:`

:rofl: `:rofl:`

:roll_eyes: `:roll_eyes:`

:roll_of_paper: `:roll_of_paper:`

:roller_coaster: `:roller_coaster:`

:roller_skate: `:roller_skate:`

:romania: `:romania:`

:rooster: `:rooster:`

:rose: `:rose:`

:rosette: `:rosette:`

:rotating_light: `:rotating_light:`

:round_pushpin: `:round_pushpin:`

:rowboat: `:rowboat:`

:rowing_man: `:rowing_man:`

:rowing_woman: `:rowing_woman:`

:ru: `:ru:`

:rugby_football: `:rugby_football:`

:runner: `:runner:`

:running: `:running:`

:running_man: `:running_man:`

:running_shirt_with_sash: `:running_shirt_with_sash:`

:running_woman: `:running_woman:`

:rwanda: `:rwanda:`

:sa: `:sa:`

:safety_pin: `:safety_pin:`

:safety_vest: `:safety_vest:`

:sagittarius: `:sagittarius:`

:sailboat: `:sailboat:`

:sake: `:sake:`

:salt: `:salt:`

:saluting_face: `:saluting_face:`

:samoa: `:samoa:`

:san_marino: `:san_marino:`

:sandal: `:sandal:`

:sandwich: `:sandwich:`

:santa: `:santa:`

:sao_tome_principe: `:sao_tome_principe:`

:sari: `:sari:`

:sassy_man: `:sassy_man:`

:sassy_woman: `:sassy_woman:`

:satellite: `:satellite:`

:satisfied: `:satisfied:`

:saudi_arabia: `:saudi_arabia:`

:sauna_man: `:sauna_man:`

:sauna_person: `:sauna_person:`

:sauna_woman: `:sauna_woman:`

:sauropod: `:sauropod:`

:saxophone: `:saxophone:`

:scarf: `:scarf:`

:school: `:school:`

:school_satchel: `:school_satchel:`

:scientist: `:scientist:`

:scissors: `:scissors:`

:scorpion: `:scorpion:`

:scorpius: `:scorpius:`

:scotland: `:scotland:`

:scream: `:scream:`

:scream_cat: `:scream_cat:`

:screwdriver: `:screwdriver:`

:scroll: `:scroll:`

:seal: `:seal:`

:seat: `:seat:`

:secret: `:secret:`

:see_no_evil: `:see_no_evil:`

:seedling: `:seedling:`

:selfie: `:selfie:`

:senegal: `:senegal:`

:serbia: `:serbia:`

:service_dog: `:service_dog:`

:seven: `:seven:`

:sewing_needle: `:sewing_needle:`

:seychelles: `:seychelles:`

:shaking_face: `:shaking_face:`

:shallow_pan_of_food: `:shallow_pan_of_food:`

:shamrock: `:shamrock:`

:shark: `:shark:`

:shaved_ice: `:shaved_ice:`

:sheep: `:sheep:`

:shell: `:shell:`

:shield: `:shield:`

:shinto_shrine: `:shinto_shrine:`

:ship: `:ship:`

:shipit: `:shipit:`

:shirt: `:shirt:`

:shit: `:shit:`

:shoe: `:shoe:`

:shopping: `:shopping:`

:shopping_cart: `:shopping_cart:`

:shorts: `:shorts:`

:shower: `:shower:`

:shrimp: `:shrimp:`

:shrug: `:shrug:`

:shushing_face: `:shushing_face:`

:sierra_leone: `:sierra_leone:`

:signal_strength: `:signal_strength:`

:singapore: `:singapore:`

:singer: `:singer:`

:sint_maarten: `:sint_maarten:`

:six: `:six:`

:six_pointed_star: `:six_pointed_star:`

:skateboard: `:skateboard:`

:ski: `:ski:`

:skier: `:skier:`

:skull: `:skull:`

:skull_and_crossbones: `:skull_and_crossbones:`

:skunk: `:skunk:`

:sled: `:sled:`

:sleeping: `:sleeping:`

:sleeping_bed: `:sleeping_bed:`

:sleepy: `:sleepy:`

:slightly_frowning_face: `:slightly_frowning_face:`

:slightly_smiling_face: `:slightly_smiling_face:`

:slot_machine: `:slot_machine:`

:sloth: `:sloth:`

:slovakia: `:slovakia:`

:slovenia: `:slovenia:`

:small_airplane: `:small_airplane:`

:small_blue_diamond: `:small_blue_diamond:`

:small_orange_diamond: `:small_orange_diamond:`

:small_red_triangle: `:small_red_triangle:`

:small_red_triangle_down: `:small_red_triangle_down:`

:smile: `:smile:`

:smile_cat: `:smile_cat:`

:smiley: `:smiley:`

:smiley_cat: `:smiley_cat:`

:smiling_face_with_tear: `:smiling_face_with_tear:`

:smiling_face_with_three_hearts: `:smiling_face_with_three_hearts:`

:smiling_imp: `:smiling_imp:`

:smirk: `:smirk:`

:smirk_cat: `:smirk_cat:`

:smoking: `:smoking:`

:snail: `:snail:`

:snake: `:snake:`

:sneezing_face: `:sneezing_face:`

:snowboarder: `:snowboarder:`

:snowflake: `:snowflake:`

:snowman: `:snowman:`

:snowman_with_snow: `:snowman_with_snow:`

:soap: `:soap:`

:sob: `:sob:`

:soccer: `:soccer:`

:socks: `:socks:`

:softball: `:softball:`

:solomon_islands: `:solomon_islands:`

:somalia: `:somalia:`

:soon: `:soon:`

:sos: `:sos:`

:sound: `:sound:`

:south_africa: `:south_africa:`

:south_georgia_south_sandwich_islands: `:south_georgia_south_sandwich_islands:`

:south_sudan: `:south_sudan:`

:space_invader: `:space_invader:`

:spades: `:spades:`

:spaghetti: `:spaghetti:`

:sparkle: `:sparkle:`

:sparkler: `:sparkler:`

:sparkles: `:sparkles:`

:sparkling_heart: `:sparkling_heart:`

:speak_no_evil: `:speak_no_evil:`

:speaker: `:speaker:`

:speaking_head: `:speaking_head:`

:speech_balloon: `:speech_balloon:`

:speedboat: `:speedboat:`

:spider: `:spider:`

:spider_web: `:spider_web:`

:spiral_calendar: `:spiral_calendar:`

:spiral_notepad: `:spiral_notepad:`

:sponge: `:sponge:`

:spoon: `:spoon:`

:squid: `:squid:`

:sri_lanka: `:sri_lanka:`

:st_barthelemy: `:st_barthelemy:`

:st_helena: `:st_helena:`

:st_kitts_nevis: `:st_kitts_nevis:`

:st_lucia: `:st_lucia:`

:st_martin: `:st_martin:`

:st_pierre_miquelon: `:st_pierre_miquelon:`

:st_vincent_grenadines: `:st_vincent_grenadines:`

:stadium: `:stadium:`

:standing_man: `:standing_man:`

:standing_person: `:standing_person:`

:standing_woman: `:standing_woman:`

:star: `:star:`

:star2: `:star2:`

:star_and_crescent: `:star_and_crescent:`

:star_of_david: `:star_of_david:`

:star_struck: `:star_struck:`

:stars: `:stars:`

:station: `:station:`

:statue_of_liberty: `:statue_of_liberty:`

:steam_locomotive: `:steam_locomotive:`

:stethoscope: `:stethoscope:`

:stew: `:stew:`

:stop_button: `:stop_button:`

:stop_sign: `:stop_sign:`

:stopwatch: `:stopwatch:`

:straight_ruler: `:straight_ruler:`

:strawberry: `:strawberry:`

:stuck_out_tongue: `:stuck_out_tongue:`

:stuck_out_tongue_closed_eyes: `:stuck_out_tongue_closed_eyes:`

:stuck_out_tongue_winking_eye: `:stuck_out_tongue_winking_eye:`

:student: `:student:`

:studio_microphone: `:studio_microphone:`

:stuffed_flatbread: `:stuffed_flatbread:`

:sudan: `:sudan:`

:sun_behind_large_cloud: `:sun_behind_large_cloud:`

:sun_behind_rain_cloud: `:sun_behind_rain_cloud:`

:sun_behind_small_cloud: `:sun_behind_small_cloud:`

:sun_with_face: `:sun_with_face:`

:sunflower: `:sunflower:`

:sunglasses: `:sunglasses:`

:sunny: `:sunny:`

:sunrise: `:sunrise:`

:sunrise_over_mountains: `:sunrise_over_mountains:`

:superhero: `:superhero:`

:superhero_man: `:superhero_man:`

:superhero_woman: `:superhero_woman:`

:supervillain: `:supervillain:`

:supervillain_man: `:supervillain_man:`

:supervillain_woman: `:supervillain_woman:`

:surfer: `:surfer:`

:surfing_man: `:surfing_man:`

:surfing_woman: `:surfing_woman:`

:suriname: `:suriname:`

:sushi: `:sushi:`

:suspect: `:suspect:`

:suspension_railway: `:suspension_railway:`

:svalbard_jan_mayen: `:svalbard_jan_mayen:`

:swan: `:swan:`

:swaziland: `:swaziland:`

:sweat: `:sweat:`

:sweat_drops: `:sweat_drops:`

:sweat_smile: `:sweat_smile:`

:sweden: `:sweden:`

:sweet_potato: `:sweet_potato:`

:swim_brief: `:swim_brief:`

:swimmer: `:swimmer:`

:swimming_man: `:swimming_man:`

:swimming_woman: `:swimming_woman:`

:switzerland: `:switzerland:`

:symbols: `:symbols:`

:synagogue: `:synagogue:`

:syria: `:syria:`

:syringe: `:syringe:`

:t-rex: `:t-rex:`

:taco: `:taco:`

:tada: `:tada:`

:taiwan: `:taiwan:`

:tajikistan: `:tajikistan:`

:takeout_box: `:takeout_box:`

:tamale: `:tamale:`

:tanabata_tree: `:tanabata_tree:`

:tangerine: `:tangerine:`

:tanzania: `:tanzania:`

:taurus: `:taurus:`

:taxi: `:taxi:`

:tea: `:tea:`

:teacher: `:teacher:`

:teapot: `:teapot:`

:technologist: `:technologist:`

:teddy_bear: `:teddy_bear:`

:telephone: `:telephone:`

:telephone_receiver: `:telephone_receiver:`

:telescope: `:telescope:`

:tennis: `:tennis:`

:tent: `:tent:`

:test_tube: `:test_tube:`

:thailand: `:thailand:`

:thermometer: `:thermometer:`

:thinking: `:thinking:`

:thong_sandal: `:thong_sandal:`

:thought_balloon: `:thought_balloon:`

:thread: `:thread:`

:three: `:three:`

:thumbsdown: `:thumbsdown:`

:thumbsup: `:thumbsup:`

:ticket: `:ticket:`

:tickets: `:tickets:`

:tiger: `:tiger:`

:tiger2: `:tiger2:`

:timer_clock: `:timer_clock:`

:timor_leste: `:timor_leste:`

:tipping_hand_man: `:tipping_hand_man:`

:tipping_hand_person: `:tipping_hand_person:`

:tipping_hand_woman: `:tipping_hand_woman:`

:tired_face: `:tired_face:`

:tm: `:tm:`

:togo: `:togo:`

:toilet: `:toilet:`

:tokelau: `:tokelau:`

:tokyo_tower: `:tokyo_tower:`

:tomato: `:tomato:`

:tonga: `:tonga:`

:tongue: `:tongue:`

:toolbox: `:toolbox:`

:tooth: `:tooth:`

:toothbrush: `:toothbrush:`

:top: `:top:`

:tophat: `:tophat:`

:tornado: `:tornado:`

:tr: `:tr:`

:trackball: `:trackball:`

:tractor: `:tractor:`

:traffic_light: `:traffic_light:`

:train: `:train:`

:train2: `:train2:`

:tram: `:tram:`

:transgender_flag: `:transgender_flag:`

:transgender_symbol: `:transgender_symbol:`

:triangular_flag_on_post: `:triangular_flag_on_post:`

:triangular_ruler: `:triangular_ruler:`

:trident: `:trident:`

:trinidad_tobago: `:trinidad_tobago:`

:tristan_da_cunha: `:tristan_da_cunha:`

:triumph: `:triumph:`

:troll: `:troll:`

:trolleybus: `:trolleybus:`

:trollface: `:trollface:`

:trophy: `:trophy:`

:tropical_drink: `:tropical_drink:`

:tropical_fish: `:tropical_fish:`

:truck: `:truck:`

:trumpet: `:trumpet:`

:tshirt: `:tshirt:`

:tulip: `:tulip:`

:tumbler_glass: `:tumbler_glass:`

:tunisia: `:tunisia:`

:turkey: `:turkey:`

:turkmenistan: `:turkmenistan:`

:turks_caicos_islands: `:turks_caicos_islands:`

:turtle: `:turtle:`

:tuvalu: `:tuvalu:`

:tv: `:tv:`

:twisted_rightwards_arrows: `:twisted_rightwards_arrows:`

:two: `:two:`

:two_hearts: `:two_hearts:`

:two_men_holding_hands: `:two_men_holding_hands:`

:two_women_holding_hands: `:two_women_holding_hands:`

:u5272: `:u5272:`

:u5408: `:u5408:`

:u55b6: `:u55b6:`

:u6307: `:u6307:`

:u6708: `:u6708:`

:u6709: `:u6709:`

:u6e80: `:u6e80:`

:u7121: `:u7121:`

:u7533: `:u7533:`

:u7981: `:u7981:`

:u7a7a: `:u7a7a:`

:uganda: `:uganda:`

:uk: `:uk:`

:ukraine: `:ukraine:`

:umbrella: `:umbrella:`

:unamused: `:unamused:`

:underage: `:underage:`

:unicorn: `:unicorn:`

:united_arab_emirates: `:united_arab_emirates:`

:united_nations: `:united_nations:`

:unlock: `:unlock:`

:up: `:up:`

:upside_down_face: `:upside_down_face:`

:uruguay: `:uruguay:`

:us: `:us:`

:us_outlying_islands: `:us_outlying_islands:`

:us_virgin_islands: `:us_virgin_islands:`

:uzbekistan: `:uzbekistan:`

:v: `:v:`

:vampire: `:vampire:`

:vampire_man: `:vampire_man:`

:vampire_woman: `:vampire_woman:`

:vanuatu: `:vanuatu:`

:vatican_city: `:vatican_city:`

:venezuela: `:venezuela:`

:vertical_traffic_light: `:vertical_traffic_light:`

:vhs: `:vhs:`

:vibration_mode: `:vibration_mode:`

:video_camera: `:video_camera:`

:video_game: `:video_game:`

:vietnam: `:vietnam:`

:violin: `:violin:`

:virgo: `:virgo:`

:volcano: `:volcano:`

:volleyball: `:volleyball:`

:vomiting_face: `:vomiting_face:`

:vs: `:vs:`

:vulcan_salute: `:vulcan_salute:`

:waffle: `:waffle:`

:wales: `:wales:`

:walking: `:walking:`

:walking_man: `:walking_man:`

:walking_woman: `:walking_woman:`

:wallis_futuna: `:wallis_futuna:`

:waning_crescent_moon: `:waning_crescent_moon:`

:waning_gibbous_moon: `:waning_gibbous_moon:`

:warning: `:warning:`

:wastebasket: `:wastebasket:`

:watch: `:watch:`

:water_buffalo: `:water_buffalo:`

:water_polo: `:water_polo:`

:watermelon: `:watermelon:`

:wave: `:wave:`

:wavy_dash: `:wavy_dash:`

:waxing_crescent_moon: `:waxing_crescent_moon:`

:waxing_gibbous_moon: `:waxing_gibbous_moon:`

:wc: `:wc:`

:weary: `:weary:`

:wedding: `:wedding:`

:weight_lifting: `:weight_lifting:`

:weight_lifting_man: `:weight_lifting_man:`

:weight_lifting_woman: `:weight_lifting_woman:`

:western_sahara: `:western_sahara:`

:whale: `:whale:`

:whale2: `:whale2:`

:wheel: `:wheel:`

:wheel_of_dharma: `:wheel_of_dharma:`

:wheelchair: `:wheelchair:`

:white_check_mark: `:white_check_mark:`

:white_circle: `:white_circle:`

:white_flag: `:white_flag:`

:white_flower: `:white_flower:`

:white_haired_man: `:white_haired_man:`

:white_haired_woman: `:white_haired_woman:`

:white_heart: `:white_heart:`

:white_large_square: `:white_large_square:`

:white_medium_small_square: `:white_medium_small_square:`

:white_medium_square: `:white_medium_square:`

:white_small_square: `:white_small_square:`

:white_square_button: `:white_square_button:`

:wilted_flower: `:wilted_flower:`

:wind_chime: `:wind_chime:`

:wind_face: `:wind_face:`

:window: `:window:`

:wine_glass: `:wine_glass:`

:wing: `:wing:`

:wink: `:wink:`

:wireless: `:wireless:`

:wolf: `:wolf:`

:woman: `:woman:`

:woman_artist: `:woman_artist:`

:woman_astronaut: `:woman_astronaut:`

:woman_beard: `:woman_beard:`

:woman_cartwheeling: `:woman_cartwheeling:`

:woman_cook: `:woman_cook:`

:woman_dancing: `:woman_dancing:`

:woman_facepalming: `:woman_facepalming:`

:woman_factory_worker: `:woman_factory_worker:`

:woman_farmer: `:woman_farmer:`

:woman_feeding_baby: `:woman_feeding_baby:`

:woman_firefighter: `:woman_firefighter:`

:woman_health_worker: `:woman_health_worker:`

:woman_in_manual_wheelchair: `:woman_in_manual_wheelchair:`

:woman_in_motorized_wheelchair: `:woman_in_motorized_wheelchair:`

:woman_in_tuxedo: `:woman_in_tuxedo:`

:woman_judge: `:woman_judge:`

:woman_juggling: `:woman_juggling:`

:woman_mechanic: `:woman_mechanic:`

:woman_office_worker: `:woman_office_worker:`

:woman_pilot: `:woman_pilot:`

:woman_playing_handball: `:woman_playing_handball:`

:woman_playing_water_polo: `:woman_playing_water_polo:`

:woman_scientist: `:woman_scientist:`

:woman_shrugging: `:woman_shrugging:`

:woman_singer: `:woman_singer:`

:woman_student: `:woman_student:`

:woman_teacher: `:woman_teacher:`

:woman_technologist: `:woman_technologist:`

:woman_with_headscarf: `:woman_with_headscarf:`

:woman_with_probing_cane: `:woman_with_probing_cane:`

:woman_with_turban: `:woman_with_turban:`

:woman_with_veil: `:woman_with_veil:`

:womans_clothes: `:womans_clothes:`

:womans_hat: `:womans_hat:`

:women_wrestling: `:women_wrestling:`

:womens: `:womens:`

:wood: `:wood:`

:woozy_face: `:woozy_face:`

:world_map: `:world_map:`

:worm: `:worm:`

:worried: `:worried:`

:wrench: `:wrench:`

:wrestling: `:wrestling:`

:writing_hand: `:writing_hand:`

:x: `:x:`

:x_ray: `:x_ray:`

:yarn: `:yarn:`

:yawning_face: `:yawning_face:`

:yellow_circle: `:yellow_circle:`

:yellow_heart: `:yellow_heart:`

:yellow_square: `:yellow_square:`

:yemen: `:yemen:`

:yen: `:yen:`

:yin_yang: `:yin_yang:`

:yo_yo: `:yo_yo:`

:yum: `:yum:`

:zambia: `:zambia:`

:zany_face: `:zany_face:`

:zap: `:zap:`

:zebra: `:zebra:`

:zero: `:zero:`

:zimbabwe: `:zimbabwe:`

:zipper_mouth_face: `:zipper_mouth_face:`

:zombie: `:zombie:`

:zombie_man: `:zombie_man:`

:zombie_woman: `:zombie_woman:`

:zzz: `:zzz:`

<!-- END: Auto-generated content (/build/emoji.js) -->

</div>
