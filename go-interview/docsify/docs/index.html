<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>docsify</title>
    <link rel="icon" href="_media/favicon.ico" />
    <meta
      name="google-site-verification"
      content="6t0LoIeFksrjF4c9sqUEsVXiQNxLp2hgoqo0KryT-sE"
    />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta
      name="keywords"
      content="doc,docs,documentation,gitbook,creator,generator,github,jekyll,github-pages"
    />
    <meta name="description" content="A magical documentation generator." />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, minimum-scale=1.0"
    />
    <link
      rel="stylesheet"
      href="//cdn.jsdelivr.net/npm/docsify@4/lib/themes/vue.css"
      title="vue"
    />
    <link
      rel="stylesheet"
      href="//cdn.jsdelivr.net/npm/docsify@4/lib/themes/dark.css"
      title="dark"
      disabled
    />
    <link
      rel="stylesheet"
      href="//cdn.jsdelivr.net/npm/docsify@4/lib/themes/buble.css"
      title="buble"
      disabled
    />
    <link
      rel="stylesheet"
      href="//cdn.jsdelivr.net/npm/docsify@4/lib/themes/pure.css"
      title="pure"
      disabled
    />
    <style>
      nav.app-nav li ul {
        min-width: 100px;
      }

      #carbonads {
        box-shadow: none !important;
        width: auto !important;
      }
    </style>
  </head>

  <body>
    <div id="app">Loading ...</div>
    <script src="//cdn.jsdelivr.net/npm/docsify-plugin-carbon@1"></script>
    <script>
      // Set html "lang" attribute based on URL
      var lang = location.hash.match(/#\/(de-de|es|ru-ru|zh-cn)\//);

      if (lang) {
        document.documentElement.setAttribute('lang', lang[1]);
      }

      // Docsify configuration
      window.$docsify = {
        alias: {
          '.*?/awesome':
            'https://raw.githubusercontent.com/docsifyjs/awesome-docsify/master/README.md',
          '.*?/changelog':
            'https://raw.githubusercontent.com/docsifyjs/docsify/master/CHANGELOG.md',
          '/.*/_navbar.md': '/_navbar.md',
          '/es/(.*)':
            'https://raw.githubusercontent.com/docsifyjs/docs-es/master/$1',
          '/de-de/(.*)':
            'https://raw.githubusercontent.com/docsifyjs/docs-de/master/$1',
          '/ru-ru/(.*)':
            'https://raw.githubusercontent.com/docsifyjs/docs-ru/master/$1',
          '/zh-cn/(.*)':
            'https://cdn.jsdelivr.net/gh/docsifyjs/docs-zh@master/$1',
        },
        auto2top: true,
        coverpage: true,
        executeScript: true,
        loadSidebar: true,
        loadNavbar: true,
        mergeNavbar: true,
        maxLevel: 4,
        subMaxLevel: 2,
        ga: 'UA-106147152-1',
        matomo: {
          host: '//matomo.thunderwave.de',
          id: 6,
        },
        name: 'docsify',
        nameLink: {
          '/es/': '#/es/',
          '/de-de/': '#/de-de/',
          '/ru-ru/': '#/ru-ru/',
          '/zh-cn/': '#/zh-cn/',
          '/': '#/',
        },
        search: {
          noData: {
            '/es/': '¡No hay resultados!',
            '/de-de/': 'Keine Ergebnisse!',
            '/ru-ru/': 'Никаких результатов!',
            '/zh-cn/': '没有结果!',
            '/': 'No results!',
          },
          paths: 'auto',
          placeholder: {
            '/es/': 'Buscar',
            '/de-de/': 'Suche',
            '/ru-ru/': 'Поиск',
            '/zh-cn/': '搜索',
            '/': 'Search',
          },
          pathNamespaces: ['/es', '/de-de', '/ru-ru', '/zh-cn'],
        },
        vueComponents: {
          'button-counter': {
            template:
              '<button @click="count += 1">You clicked me {{ count }} times</button>',
            data: function () {
              return {
                count: 0,
              };
            },
          },
        },
        vueGlobalOptions: {
          data: function () {
            return {
              count: 0,
              message: 'Hello, World!',
              // Fake API response
              images: [
                { title: 'Image 1', url: 'https://picsum.photos/150?random=1' },
                { title: 'Image 2', url: 'https://picsum.photos/150?random=2' },
                { title: 'Image 3', url: 'https://picsum.photos/150?random=3' },
              ],
            };
          },
          computed: {
            timeOfDay: function () {
              const date = new Date();
              const hours = date.getHours();

              if (hours < 12) {
                return 'morning';
              } else if (hours < 18) {
                return 'afternoon';
              } else {
                return 'evening';
              }
            },
          },
          methods: {
            hello: function () {
              alert(this.message);
            },
          },
        },
        vueMounts: {
          '#counter': {
            data: function () {
              return {
                count: 0,
              };
            },
          },
        },
        plugins: [
          DocsifyCarbon.create('CEBI6KQE', 'docsifyjsorg'),
          function (hook, vm) {
            hook.beforeEach(function (html) {
              if (/githubusercontent\.com/.test(vm.route.file)) {
                url = vm.route.file
                  .replace('raw.githubusercontent.com', 'github.com')
                  .replace(/\/master/, '/blob/master');
              } else if (/jsdelivr\.net/.test(vm.route.file)) {
                url = vm.route.file
                  .replace('cdn.jsdelivr.net/gh', 'github.com')
                  .replace('@master', '/blob/master');
              } else {
                url =
                  'https://github.com/docsifyjs/docsify/blob/develop/docs/' +
                  vm.route.file;
              }
              var editHtml = '[:memo: Edit Document](' + url + ')\n';
              return (
                editHtml +
                html +
                '\n\n----\n\n' +
                '<a href="https://docsify.js.org" target="_blank" style="color: inherit; font-weight: normal; text-decoration: none;">Powered by docsify</a>'
              );
            })
          },
        ],
      };
    </script>
    <script src="//cdn.jsdelivr.net/npm/docsify@4/lib/docsify.min.js"></script>
    <script src="//cdn.jsdelivr.net/npm/docsify@4/lib/plugins/search.min.js"></script>
    <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-bash.min.js"></script>
    <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-markdown.min.js"></script>
    <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-nginx.min.js"></script>
    <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-php.min.js"></script>
    <script>
      // Public site only
      if (/docsify/.test(location.host)) {
        document.write(
          '<script src="//cdn.jsdelivr.net/npm/docsify@4/lib/plugins/ga.min.js"><\/script>'
        );
        document.write(
          '<script src="//cdn.jsdelivr.net/npm/docsify@4/lib/plugins/matomo.min.js"><\/script>'
        );
      }
    </script>
    <script src="//cdn.jsdelivr.net/npm/vue@2/dist/vue.min.js"></script>
    <!-- <script src="//cdn.jsdelivr.net/npm/vue@3/dist/vue.global.prod.js"></script> -->
  </body>
</html>
