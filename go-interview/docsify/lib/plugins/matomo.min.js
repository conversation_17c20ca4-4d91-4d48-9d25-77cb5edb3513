!function(){function o(n){window._paq=window._paq||[],window._paq.push(["trackPageView"]),window._paq.push(["enableLinkTracking"]),setTimeout(function(){var o,i;o=n,(i=document.createElement("script")).async=!0,i.src=o.host+"/matomo.js",document.body.appendChild(i),window._paq.push(["setTrackerUrl",n.host+"/matomo.php"]),window._paq.push(["setSiteId",String(n.id)])},0)}function i(){window._paq||o($docsify.matomo),window._paq.push(["setCustomUrl",window.location.hash.substr(1)]),window._paq.push(["setDocumentTitle",document.title]),window._paq.push(["trackPageView"])}$docsify.plugins=[].concat(function(o){$docsify.matomo?o.beforeEach(i):console.error("[Docsify] matomo is required.")},$docsify.plugins)}();
