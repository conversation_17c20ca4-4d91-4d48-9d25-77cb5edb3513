!function(){var x=[],y=[],n=0,R={regLevel:new RegExp("^([\\s\\-]+)"),invalidLine:new RegExp("^\\-\\-\\-|^\\.\\.\\.|^\\s*#.*|^\\s*$"),dashesString:new RegExp('^\\s*\\"([^\\"]*)\\"\\s*$'),quotesString:new RegExp("^\\s*\\'([^\\']*)\\'\\s*$"),float:new RegExp("^[+-]?[0-9]+\\.[0-9]+(e[+-]?[0-9]+(\\.[0-9]+)?)?$"),integer:new RegExp("^[+-]?[0-9]+$"),array:new RegExp("\\[\\s*(.*)\\s*\\]"),map:new RegExp("\\{\\s*(.*)\\s*\\}"),key_value:new RegExp("([a-z0-9_-][ a-z0-9_-]*):( .+)","i"),single_key_value:new RegExp("^([a-z0-9_-][ a-z0-9_-]*):( .+?)$","i"),key:new RegExp("([a-z0-9_-][ a-z0-9_-]+):( .+)?","i"),item:new RegExp("^-\\s+"),trim:new RegExp("^\\s+|\\s+$"),comment:new RegExp("([^\\'\\\"#]+([\\'\\\"][^\\'\\\"]*[\\'\\\"])*)*(#.*)?")};function v(e){return{parent:null,length:0,level:e,lines:[],children:[],addChild:function(e){this.children.push(e),++(e.parent=this).length}}}function N(e){var n=null;if("true"==(e=e.replace(R.trim,"")))return!0;if("false"==e)return!1;if(".NaN"==e)return Number.NaN;if("null"==e)return null;if(".inf"==e)return Number.POSITIVE_INFINITY;if("-.inf"==e)return Number.NEGATIVE_INFINITY;if(n=e.match(R.dashesString))return n[1];if(n=e.match(R.quotesString))return n[1];if(n=e.match(R.float))return parseFloat(n[0]);if(n=e.match(R.integer))return parseInt(n[0]);if(isNaN(n=Date.parse(e))){if(n=e.match(R.single_key_value))return(i={})[n[1]]=N(n[2]),i;if(n=e.match(R.array)){for(var t=0,r=" ",i=[],l="",u=!1,a=0,s=n[1].length;a<s;++a){if("'"==(r=n[1][a])||'"'==r){if(!1===u){l+=u=r;continue}if("'"==r&&"'"==u||'"'==r&&'"'==u){u=!1,l+=r;continue}}else if(!1!==u||"["!=r&&"{"!=r)if(!1!==u||"]"!=r&&"}"!=r){if(!1===u&&0==t&&","==r){i.push(N(l)),l="";continue}}else--t;else++t;l+=r}return 0<l.length&&i.push(N(l)),i}if(n=e.match(R.map)){for(t=0,r=" ",i=[],l="",u=!1,a=0,s=n[1].length;a<s;++a){if("'"==(r=n[1][a])||'"'==r){if(!1===u){l+=u=r;continue}if("'"==r&&"'"==u||'"'==r&&'"'==u){u=!1,l+=r;continue}}else if(!1!==u||"["!=r&&"{"!=r)if(!1!==u||"]"!=r&&"}"!=r){if(!1===u&&0==t&&","==r){i.push(l),l="";continue}}else--t;else++t;l+=r}0<l.length&&i.push(l);for(var f={},a=0,s=i.length;a<s;++a)(n=i[a].match(R.key_value))&&(f[n[1]]=N(n[2]));return f}return e}return new Date(n)}function _(e){for(var n=e.lines,t=e.children,r=[n.join(" ")],i=0,l=t.length;i<l;++i)r.push(_(t[i]));return r.join("\n")}function $(e){for(var n=e.lines,t=e.children,r=n.join("\n"),i=0,l=t.length;i<l;++i)r+=$(t[i]);return r}function t(e){return function e(n){for(var t=null,r={},i=null,l=null,u=-1,a=[],s=!0,f=0,h=n.length;f<h;++f)if(-1==u||u==n[f].level){a.push(f);for(var u=n[f].level,o=n[f].lines,i=n[f].children,l=null,c=0,p=o.length;c<p;++c){var g=o[c];if(t=g.match(R.key)){var v=t[1];if("-"==v[0]&&(v=v.replace(R.item,""),s&&(s=!1,void 0===r.length&&(r=[])),null!=l&&r.push(l),l={},s=!0),void 0!==t[2]){var d=t[2].replace(R.trim,"");if("&"==d[0]){var m=e(i);null!=l?l[v]=m:r[v]=m,y[d.substr(1)]=m}else if("|"==d[0])null!=l?l[v]=$(i.shift()):r[v]=$(i.shift());else if("*"==d[0]){var w=d.substr(1),E={};if(void 0===y[w])x.push("Reference '"+w+"' not found!");else{for(var b in y[w])E[b]=y[w][b];null!=l?l[v]=E:r[v]=E}}else">"==d[0]?null!=l?l[v]=_(i.shift()):r[v]=_(i.shift()):null!=l?l[v]=N(d):r[v]=N(d)}else null!=l?l[v]=e(i):r[v]=e(i)}else g.match(/^-\s*$/)?(s&&(s=!1,void 0===r.length&&(r=[])),null!=l&&r.push(l),l={},s=!0):(t=g.match(/^-\s*(.*)/))&&(null!=l?l.push(N(t[1])):(s&&(s=!1,void 0===r.length&&(r=[])),r.push(N(t[1]))))}null!=l&&(s&&(s=!1,void 0===r.length&&(r=[])),r.push(l))}for(f=a.length-1;0<=f;--f)n.splice.call(n,a[f],1);return r}(e.children)}function i(e){x=[],y=[],n=(new Date).getTime();e=t(function(e){var n=R.regLevel,t=R.invalidLine,r=e.split("\n"),i=0,l=0,u=[],e=new v(-1),a=new v(0);e.addChild(a);var s=[],f="";u.push(a),s.push(i);for(var h=0,o=r.length;h<o;++h)if(!(f=r[h]).match(t)){if(l<(i=(c=n.exec(f))?c[1].length:0)){var c=a,a=new v(i);c.addChild(a),u.push(a),s.push(i)}else if(i<l){for(var p=!1,g=s.length-1;0<=g;--g)if(s[g]==i){a=new v(i),u.push(a),s.push(i),null!=u[g].parent&&u[g].parent.addChild(a),p=!0;break}if(!p)return void x.push("Error: Invalid indentation at line "+h+": "+f)}a.lines.push(f.replace(R.trim,"")),l=i}return e}(function(e){var n,t,r=e.split("\n"),i=R.comment;for(t in r)(n=r[t].match(i))&&void 0!==n[3]&&(r[t]=n[0].substr(0,n[0].length-n[3].length));return r.join("\n")}(e)));return n=(new Date).getTime()-n,e}var l=new RegExp("^(\\ufeff?(= yaml =|---)$([\\s\\S]*?)(?:\\2|\\.\\.\\.)$(?:\\n)?)","m");function r(e){var n=(e=e||"").split(/(\r?\n)/);return n[0]&&/= yaml =|---/.test(n[0])?function(e){var n=l.exec(e);if(!n)return{attributes:{},body:e};var t=n[n.length-1].replace(/^\s+|\s+$/g,""),r=i(t)||{},n=e.replace(n[0],"");return{attributes:r,body:n,frontmatter:t}}(e):{attributes:{},body:e}}$docsify.plugins=[].concat(function(e,t){t.config.frontMatter={},t.config.frontMatter.installed=!0,t.config.frontMatter.parseMarkdown=function(e){return r(e).body},e.beforeEach(function(e){var n=r(e),e=n.attributes,n=n.body;return t.frontmatter=e,n})},$docsify.plugins)}();
