
<a name="3.7.3"></a>
## [3.7.3](https://github.com/QingWei-Li/docsify/compare/v3.7.2...v3.7.3) (2017-05-22)


### Bug Fixes

* **render:** find => filter ([eca3368](https://github.com/QingWei-Li/docsify/commit/eca3368))



<a name="3.7.2"></a>
## [3.7.2](https://github.com/QingWei-Li/docsify/compare/v3.7.1...v3.7.2) (2017-05-19)



<a name="3.7.1"></a>
## [3.7.1](https://github.com/QingWei-Li/docsify/compare/v3.7.0...v3.7.1) (2017-05-19)


### Bug Fixes

* docsify-updated is undefined ([b2b4742](https://github.com/QingWei-Li/docsify/commit/b2b4742))



<a name="3.7.0"></a>
# [3.7.0](https://github.com/QingWei-Li/docsify/compare/v3.6.6...v3.7.0) (2017-05-16)


### Features

* add docsify-updated, close [#158](https://github.com/QingWei-Li/docsify/issues/158) ([d2be5ae](https://github.com/QingWei-Li/docsify/commit/d2be5ae))
* add externalLinkTarget, close [#149](https://github.com/QingWei-Li/docsify/issues/149) ([2d73285](https://github.com/QingWei-Li/docsify/commit/2d73285))



<a name="3.6.6"></a>
## [3.6.6](https://github.com/QingWei-Li/docsify/compare/v3.6.5...v3.6.6) (2017-05-06)


### Features

* support query string for the search, fixed [#156](https://github.com/QingWei-Li/docsify/issues/156) ([da75d70](https://github.com/QingWei-Li/docsify/commit/da75d70))



<a name="3.6.5"></a>
## [3.6.5](https://github.com/QingWei-Li/docsify/compare/v3.6.4...v3.6.5) (2017-04-28)


### Bug Fixes

* **util:** fix crash, fixed [#154](https://github.com/QingWei-Li/docsify/issues/154) ([51832d3](https://github.com/QingWei-Li/docsify/commit/51832d3))



<a name="3.6.4"></a>
## [3.6.4](https://github.com/QingWei-Li/docsify/compare/v3.6.3...v3.6.4) (2017-04-28)


### Bug Fixes

* **util:** correctly clean up duplicate slashes, fixed [#153](https://github.com/QingWei-Li/docsify/issues/153) ([76c041a](https://github.com/QingWei-Li/docsify/commit/76c041a))



<a name="3.6.3"></a>
## [3.6.3](https://github.com/QingWei-Li/docsify/compare/v3.6.2...v3.6.3) (2017-04-25)


### Bug Fixes

* **external-script:** script attrs ([2653849](https://github.com/QingWei-Li/docsify/commit/2653849))



<a name="3.6.2"></a>
## [3.6.2](https://github.com/QingWei-Li/docsify/compare/v3.6.0...v3.6.2) (2017-04-12)


### Features

* **event:** Collapse the sidebar when click outside element in the small screen ([9b7e5f5](https://github.com/QingWei-Li/docsify/commit/9b7e5f5))
* **external-script:** detect more than one script dom, fixed [#146](https://github.com/QingWei-Li/docsify/issues/146) ([94d6603](https://github.com/QingWei-Li/docsify/commit/94d6603))



<a name="3.6.0"></a>
# [3.6.0](https://github.com/QingWei-Li/docsify/compare/v3.5.2...v3.6.0) (2017-04-09)


### Features

* **render:** add mergeNavbar option, close [#125](https://github.com/QingWei-Li/docsify/issues/125), [#124](https://github.com/QingWei-Li/docsify/issues/124) ([#145](https://github.com/QingWei-Li/docsify/issues/145)) ([9220523](https://github.com/QingWei-Li/docsify/commit/9220523))



<a name="3.5.2"></a>
## [3.5.2](https://github.com/QingWei-Li/docsify/compare/v3.5.1...v3.5.2) (2017-04-05)



<a name="3.5.1"></a>
## [3.5.1](https://github.com/QingWei-Li/docsify/compare/v3.5.0...v3.5.1) (2017-03-25)


### Bug Fixes

* .md file extension regex ([594299f](https://github.com/QingWei-Li/docsify/commit/594299f))



<a name="3.5.0"></a>
# [3.5.0](https://github.com/QingWei-Li/docsify/compare/v3.4.4...v3.5.0) (2017-03-25)


### Bug Fixes

* adjust display on small screens ([bf35471](https://github.com/QingWei-Li/docsify/commit/bf35471))
* navbar labels for German ([b022aaf](https://github.com/QingWei-Li/docsify/commit/b022aaf))


### Features

* **route:** auto remove .md extension ([8f11653](https://github.com/QingWei-Li/docsify/commit/8f11653))



<a name="3.4.4"></a>
## [3.4.4](https://github.com/QingWei-Li/docsify/compare/v3.4.3...v3.4.4) (2017-03-17)


### Bug Fixes

* **search:** fix input style ([2d6a51b](https://github.com/QingWei-Li/docsify/commit/2d6a51b))



<a name="3.4.3"></a>
## [3.4.3](https://github.com/QingWei-Li/docsify/compare/v3.4.2...v3.4.3) (2017-03-16)



<a name="3.4.2"></a>
## [3.4.2](https://github.com/QingWei-Li/docsify/compare/v3.4.1...v3.4.2) (2017-03-11)


### Features

* **emojify:** add no-emoji option ([3aef37a](https://github.com/QingWei-Li/docsify/commit/3aef37a))



<a name="3.4.1"></a>
## [3.4.1](https://github.com/QingWei-Li/docsify/compare/v3.4.0...v3.4.1) (2017-03-10)


### Bug Fixes

* **dom:** Disable the dom cache when vue is present, fixed [#119](https://github.com/QingWei-Li/docsify/issues/119) ([b9a7275](https://github.com/QingWei-Li/docsify/commit/b9a7275))



<a name="3.4.0"></a>
# [3.4.0](https://github.com/QingWei-Li/docsify/compare/v3.3.0...v3.4.0) (2017-03-09)


### Features

* **zoom-image:** add plugin ([50fa6fc](https://github.com/QingWei-Li/docsify/commit/50fa6fc))



<a name="3.3.0"></a>
# [3.3.0](https://github.com/QingWei-Li/docsify/compare/v3.2.0...v3.3.0) (2017-03-07)



<a name="3.2.0"></a>
# [3.2.0](https://github.com/QingWei-Li/docsify/compare/v3.1.2...v3.2.0) (2017-02-28)


### Bug Fixes

* **fetch:** load sidebar and navbar for parent path, fixed [#100](https://github.com/QingWei-Li/docsify/issues/100) ([f3fc596](https://github.com/QingWei-Li/docsify/commit/f3fc596))
* **render:** Toc rendering error, fixed [#106](https://github.com/QingWei-Li/docsify/issues/106) ([0d59ee9](https://github.com/QingWei-Li/docsify/commit/0d59ee9))


### Features

* **search:** Localization for no data tip, close [#103](https://github.com/QingWei-Li/docsify/issues/103) ([d3c9fbd](https://github.com/QingWei-Li/docsify/commit/d3c9fbd))



<a name="3.1.2"></a>
## [3.1.2](https://github.com/QingWei-Li/docsify/compare/v3.1.1...v3.1.2) (2017-02-27)



<a name="3.1.1"></a>
## [3.1.1](https://github.com/QingWei-Li/docsify/compare/v3.1.0...v3.1.1) (2017-02-24)


### Bug Fixes

* **render:** custom cover background image ([8f9bf29](https://github.com/QingWei-Li/docsify/commit/8f9bf29))
* **search:** don't search nameLink, fixed [#102](https://github.com/QingWei-Li/docsify/issues/102) ([507d9e8](https://github.com/QingWei-Li/docsify/commit/507d9e8))
* **tpl:** extra character, fixed [#101](https://github.com/QingWei-Li/docsify/issues/101) ([d67d25f](https://github.com/QingWei-Li/docsify/commit/d67d25f))



<a name="3.1.0"></a>
# [3.1.0](https://github.com/QingWei-Li/docsify/compare/v3.0.5...v3.1.0) (2017-02-22)


### Bug Fixes

* **search:** incorrect anchor link, fixed [#90](https://github.com/QingWei-Li/docsify/issues/90) ([b8a3d8f](https://github.com/QingWei-Li/docsify/commit/b8a3d8f))
* **sw:** update white list ([f2975a5](https://github.com/QingWei-Li/docsify/commit/f2975a5))


### Features

* **emoji:** add emoji plugin ([855c450](https://github.com/QingWei-Li/docsify/commit/855c450))



<a name="3.0.5"></a>
## [3.0.5](https://github.com/QingWei-Li/docsify/compare/v3.0.4...v3.0.5) (2017-02-21)


### Bug Fixes

* **event:** highlight sidebar when clicked, fixed [#86](https://github.com/QingWei-Li/docsify/issues/86) ([2a1157a](https://github.com/QingWei-Li/docsify/commit/2a1157a))
* **gen-tree:** cache toc list, fixed [#88](https://github.com/QingWei-Li/docsify/issues/88) ([3394ebb](https://github.com/QingWei-Li/docsify/commit/3394ebb))
* **layout.css:** loading style ([42b2dba](https://github.com/QingWei-Li/docsify/commit/42b2dba))


### Features

* **pwa:** add sw.js ([f7111b5](https://github.com/QingWei-Li/docsify/commit/f7111b5))



<a name="3.0.4"></a>
## [3.0.4](https://github.com/QingWei-Li/docsify/compare/v3.0.3...v3.0.4) (2017-02-20)


### Bug Fixes

* **render:** disable rendering sub list when loadSidebar is false ([35dd2e1](https://github.com/QingWei-Li/docsify/commit/35dd2e1))
* **render:** execute script ([780c1e5](https://github.com/QingWei-Li/docsify/commit/780c1e5))



<a name="3.0.3"></a>
## [3.0.3](https://github.com/QingWei-Li/docsify/compare/v3.0.2...v3.0.3) (2017-02-19)



<a name="3.0.2"></a>
## [3.0.2](https://github.com/QingWei-Li/docsify/compare/v3.0.1...v3.0.2) (2017-02-19)


### Bug Fixes

* **compiler:** link ([3b127a1](https://github.com/QingWei-Li/docsify/commit/3b127a1))
* **search:** add lazy input ([bf593a7](https://github.com/QingWei-Li/docsify/commit/bf593a7))



<a name="3.0.1"></a>
## [3.0.1](https://github.com/QingWei-Li/docsify/compare/v3.0.0...v3.0.1) (2017-02-19)


### Bug Fixes

* **route:** empty alias ([cd99b52](https://github.com/QingWei-Li/docsify/commit/cd99b52))



<a name="3.0.0"></a>
# [3.0.0](https://github.com/QingWei-Li/docsify/compare/v2.4.3...v3.0.0) (2017-02-19)


### Bug Fixes

* **compiler:** link ([c7e09c3](https://github.com/QingWei-Li/docsify/commit/c7e09c3))
* **render:** support html file ([7b6a2ac](https://github.com/QingWei-Li/docsify/commit/7b6a2ac))
* **search:** escape html ([fcb66e8](https://github.com/QingWei-Li/docsify/commit/fcb66e8))
* **search:** fix default config ([2efd859](https://github.com/QingWei-Li/docsify/commit/2efd859))


### Features

* **front-matter:** add front matter[WIP] ([dbb9278](https://github.com/QingWei-Li/docsify/commit/dbb9278))
* **render:** add auto header ([b7768b1](https://github.com/QingWei-Li/docsify/commit/b7768b1))
* **search:** Localization for search placeholder, close [#80](https://github.com/QingWei-Li/docsify/issues/80) ([2351c3e](https://github.com/QingWei-Li/docsify/commit/2351c3e))
* **themes:** add loading info ([86594a3](https://github.com/QingWei-Li/docsify/commit/86594a3))



<a name="2.4.3"></a>
## [2.4.3](https://github.com/QingWei-Li/docsify/compare/v2.4.2...v2.4.3) (2017-02-15)



<a name="2.4.2"></a>
## [2.4.2](https://github.com/QingWei-Li/docsify/compare/v2.4.1...v2.4.2) (2017-02-14)


### Bug Fixes

* **index:** load file path error ([dc536a3](https://github.com/QingWei-Li/docsify/commit/dc536a3))



<a name="2.4.1"></a>
## [2.4.1](https://github.com/QingWei-Li/docsify/compare/v2.4.0...v2.4.1) (2017-02-13)


### Bug Fixes

* **index:** cover page ([dd0c84b](https://github.com/QingWei-Li/docsify/commit/dd0c84b))



<a name="2.4.0"></a>
# [2.4.0](https://github.com/QingWei-Li/docsify/compare/v2.3.0...v2.4.0) (2017-02-13)


### Features

* **hook:** add doneEach ([c6f7602](https://github.com/QingWei-Li/docsify/commit/c6f7602))



<a name="2.3.0"></a>
# [2.3.0](https://github.com/QingWei-Li/docsify/compare/v2.2.1...v2.3.0) (2017-02-13)


### Bug Fixes

* **event:**  has no effect on a FF mobile browser, fixed [#67](https://github.com/QingWei-Li/docsify/issues/67) ([0ff36c2](https://github.com/QingWei-Li/docsify/commit/0ff36c2))
* **render:** custom marked renderer ([bf559b4](https://github.com/QingWei-Li/docsify/commit/bf559b4))
* **render:** fix render link ([a866744](https://github.com/QingWei-Li/docsify/commit/a866744))
* **render:** image url ([6f87529](https://github.com/QingWei-Li/docsify/commit/6f87529))
* **render:** render link ([38ea660](https://github.com/QingWei-Li/docsify/commit/38ea660))
* **src:** fix route ([324301a](https://github.com/QingWei-Li/docsify/commit/324301a))
* **src:** get alias ([784173e](https://github.com/QingWei-Li/docsify/commit/784173e))
* **src:** get alias ([ce99a04](https://github.com/QingWei-Li/docsify/commit/ce99a04))
* **themes:** fix navbar style ([fa54b52](https://github.com/QingWei-Li/docsify/commit/fa54b52))
* **themes:** update navbar style ([4864d1b](https://github.com/QingWei-Li/docsify/commit/4864d1b))


### Features

* **hook:** support custom plugin ([9e81a59](https://github.com/QingWei-Li/docsify/commit/9e81a59))
* **src:** add alias feature ([24412cd](https://github.com/QingWei-Li/docsify/commit/24412cd))
* **src:** dynamic title and fix sidebar style ([6b30eb6](https://github.com/QingWei-Li/docsify/commit/6b30eb6))



<a name="2.2.1"></a>
## [2.2.1](https://github.com/QingWei-Li/docsify/compare/v2.2.0...v2.2.1) (2017-02-11)


### Bug Fixes

* **event:** scroll active sidebar ([50f5fc2](https://github.com/QingWei-Li/docsify/commit/50f5fc2))
* **search:** crash when not content, fixed [#68](https://github.com/QingWei-Li/docsify/issues/68) ([9d3cc89](https://github.com/QingWei-Li/docsify/commit/9d3cc89))
* **search:** not work in mobile ([3941304](https://github.com/QingWei-Li/docsify/commit/3941304))



<a name="2.2.0"></a>
# [2.2.0](https://github.com/QingWei-Li/docsify/compare/v2.1.0...v2.2.0) (2017-02-09)


### Features

* **plugins:** add Google Analytics plugin ([#66](https://github.com/QingWei-Li/docsify/issues/66)) ([ac61bb0](https://github.com/QingWei-Li/docsify/commit/ac61bb0))



<a name="2.1.0"></a>
# [2.1.0](https://github.com/QingWei-Li/docsify/compare/v2.0.3...v2.1.0) (2017-02-09)


### Bug Fixes

* render name ([12e2479](https://github.com/QingWei-Li/docsify/commit/12e2479))
* **vue.css:** update sidebar style ([fc140ef](https://github.com/QingWei-Li/docsify/commit/fc140ef))


### Features

* add search, close [#43](https://github.com/QingWei-Li/docsify/issues/43) ([eb5ff3e](https://github.com/QingWei-Li/docsify/commit/eb5ff3e))



<a name="2.0.3"></a>
## [2.0.3](https://github.com/QingWei-Li/docsify/compare/v2.0.2...v2.0.3) (2017-02-07)


### Bug Fixes

* css var polyfill ([8cd386a](https://github.com/QingWei-Li/docsify/commit/8cd386a))
* css var polyfill ([cbaee21](https://github.com/QingWei-Li/docsify/commit/cbaee21))
* rendering emojis ([8c7e4d7](https://github.com/QingWei-Li/docsify/commit/8c7e4d7))



<a name="2.0.2"></a>
## [2.0.2](https://github.com/QingWei-Li/docsify/compare/v2.0.1...v2.0.2) (2017-02-05)


### Bug Fixes

* button style in cover page ([4470855](https://github.com/QingWei-Li/docsify/commit/4470855))



<a name="2.0.1"></a>
## [2.0.1](https://github.com/QingWei-Li/docsify/compare/v2.0.0...v2.0.1) (2017-02-05)



<a name="2.0.0"></a>
# [2.0.0](https://github.com/QingWei-Li/docsify/compare/v1.10.5...v2.0.0) (2017-02-05)


### Features

* customize the theme color ([5cc9f05](https://github.com/QingWei-Li/docsify/commit/5cc9f05))



<a name="1.10.5"></a>
## [1.10.5](https://github.com/QingWei-Li/docsify/compare/v1.10.4...v1.10.5) (2017-01-28)



<a name="1.10.4"></a>
## [1.10.4](https://github.com/QingWei-Li/docsify/compare/v1.10.3...v1.10.4) (2017-01-27)



<a name="1.10.3"></a>
## [1.10.3](https://github.com/QingWei-Li/docsify/compare/v1.10.2...v1.10.3) (2017-01-27)



<a name="1.10.2"></a>
## [1.10.2](https://github.com/QingWei-Li/docsify/compare/v1.10.1...v1.10.2) (2017-01-25)



<a name="1.10.1"></a>
## [1.10.1](https://github.com/QingWei-Li/docsify/compare/v1.10.0...v1.10.1) (2017-01-25)



<a name="1.10.0"></a>
# [1.10.0](https://github.com/QingWei-Li/docsify/compare/v1.9.0...v1.10.0) (2017-01-25)



<a name="1.9.0"></a>
# [1.9.0](https://github.com/QingWei-Li/docsify/compare/v1.8.0...v1.9.0) (2017-01-24)



<a name="1.8.0"></a>
# [1.8.0](https://github.com/QingWei-Li/docsify/compare/v1.7.4...v1.8.0) (2017-01-24)



<a name="1.7.4"></a>
## [1.7.4](https://github.com/QingWei-Li/docsify/compare/v1.7.3...v1.7.4) (2017-01-13)



<a name="1.7.3"></a>
## [1.7.3](https://github.com/QingWei-Li/docsify/compare/v1.7.2...v1.7.3) (2017-01-13)



<a name="1.7.2"></a>
## [1.7.2](https://github.com/QingWei-Li/docsify/compare/v1.7.1...v1.7.2) (2017-01-12)



<a name="1.7.1"></a>
## [1.7.1](https://github.com/QingWei-Li/docsify/compare/v1.7.0...v1.7.1) (2017-01-12)



<a name="1.7.0"></a>
# [1.7.0](https://github.com/QingWei-Li/docsify/compare/v1.6.1...v1.7.0) (2017-01-12)



<a name="1.6.1"></a>
## [1.6.1](https://github.com/QingWei-Li/docsify/compare/v1.6.0...v1.6.1) (2017-01-10)



<a name="1.6.0"></a>
# [1.6.0](https://github.com/QingWei-Li/docsify/compare/v1.5.2...v1.6.0) (2017-01-10)



<a name="1.5.2"></a>
## [1.5.2](https://github.com/QingWei-Li/docsify/compare/v1.5.1...v1.5.2) (2017-01-10)



<a name="1.5.1"></a>
## [1.5.1](https://github.com/QingWei-Li/docsify/compare/v1.5.0...v1.5.1) (2017-01-09)



<a name="1.5.0"></a>
# [1.5.0](https://github.com/QingWei-Li/docsify/compare/v1.4.3...v1.5.0) (2017-01-04)


### Features

* Markdown parser is configurable, [#42](https://github.com/QingWei-Li/docsify/issues/42) ([8b1000a](https://github.com/QingWei-Li/docsify/commit/8b1000a))



<a name="1.4.3"></a>
## [1.4.3](https://github.com/QingWei-Li/docsify/compare/v1.4.2...v1.4.3) (2017-01-01)



<a name="1.4.2"></a>
## [1.4.2](https://github.com/QingWei-Li/docsify/compare/v1.4.1...v1.4.2) (2016-12-31)



<a name="1.4.1"></a>
## [1.4.1](https://github.com/QingWei-Li/docsify/compare/v1.4.0...v1.4.1) (2016-12-31)



<a name="1.4.0"></a>
# [1.4.0](https://github.com/QingWei-Li/docsify/compare/v1.3.5...v1.4.0) (2016-12-31)



<a name="1.3.5"></a>
## [1.3.5](https://github.com/QingWei-Li/docsify/compare/v1.3.4...v1.3.5) (2016-12-25)



<a name="1.3.4"></a>
## [1.3.4](https://github.com/QingWei-Li/docsify/compare/v1.3.3...v1.3.4) (2016-12-25)



<a name="1.3.3"></a>
## [1.3.3](https://github.com/QingWei-Li/docsify/compare/v1.3.2...v1.3.3) (2016-12-23)



<a name="1.3.2"></a>
## [1.3.2](https://github.com/QingWei-Li/docsify/compare/v1.3.1...v1.3.2) (2016-12-22)



<a name="1.3.1"></a>
## [1.3.1](https://github.com/QingWei-Li/docsify/compare/v1.3.0...v1.3.1) (2016-12-22)



<a name="1.3.0"></a>
# [1.3.0](https://github.com/QingWei-Li/docsify/compare/v1.2.0...v1.3.0) (2016-12-21)



<a name="1.2.0"></a>
# [1.2.0](https://github.com/QingWei-Li/docsify/compare/v1.1.7...v1.2.0) (2016-12-20)



<a name="1.1.7"></a>
## [1.1.7](https://github.com/QingWei-Li/docsify/compare/v1.1.6...v1.1.7) (2016-12-19)



<a name="1.1.6"></a>
## [1.1.6](https://github.com/QingWei-Li/docsify/compare/v1.1.5...v1.1.6) (2016-12-18)



<a name="1.1.5"></a>
## [1.1.5](https://github.com/QingWei-Li/docsify/compare/v1.1.4...v1.1.5) (2016-12-18)



<a name="1.1.4"></a>
## [1.1.4](https://github.com/QingWei-Li/docsify/compare/v1.1.3...v1.1.4) (2016-12-17)



<a name="1.1.3"></a>
## [1.1.3](https://github.com/QingWei-Li/docsify/compare/v1.1.2...v1.1.3) (2016-12-17)



<a name="1.1.2"></a>
## [1.1.2](https://github.com/QingWei-Li/docsify/compare/v1.1.1...v1.1.2) (2016-12-17)



<a name="1.1.1"></a>
## [1.1.1](https://github.com/QingWei-Li/docsify/compare/v1.1.0...v1.1.1) (2016-12-17)



<a name="1.1.0"></a>
# [1.1.0](https://github.com/QingWei-Li/docsify/compare/v1.0.3...v1.1.0) (2016-12-16)



<a name="1.0.3"></a>
## [1.0.3](https://github.com/QingWei-Li/docsify/compare/v1.0.2...v1.0.3) (2016-12-13)



<a name="1.0.2"></a>
## [1.0.2](https://github.com/QingWei-Li/docsify/compare/v1.0.1...v1.0.2) (2016-12-13)



<a name="1.0.1"></a>
## [1.0.1](https://github.com/QingWei-Li/docsify/compare/v1.0.0...v1.0.1) (2016-12-08)



<a name="1.0.0"></a>
# [1.0.0](https://github.com/QingWei-Li/docsify/compare/v0.7.0...v1.0.0) (2016-12-08)



<a name="0.7.0"></a>
# [0.7.0](https://github.com/QingWei-Li/docsify/compare/v0.6.1...v0.7.0) (2016-11-30)



<a name="0.6.1"></a>
## [0.6.1](https://github.com/QingWei-Li/docsify/compare/v0.6.0...v0.6.1) (2016-11-29)



<a name="0.6.0"></a>
# [0.6.0](https://github.com/QingWei-Li/docsify/compare/v0.5.0...v0.6.0) (2016-11-29)



<a name="0.5.0"></a>
# [0.5.0](https://github.com/QingWei-Li/docsify/compare/v0.4.2...v0.5.0) (2016-11-28)



<a name="0.4.2"></a>
## [0.4.2](https://github.com/QingWei-Li/docsify/compare/v0.4.1...v0.4.2) (2016-11-28)



<a name="0.4.1"></a>
## [0.4.1](https://github.com/QingWei-Li/docsify/compare/v0.4.0...v0.4.1) (2016-11-28)



<a name="0.4.0"></a>
# [0.4.0](https://github.com/QingWei-Li/docsify/compare/v0.3.1...v0.4.0) (2016-11-27)


### Features

* custom sidebar, [#4](https://github.com/QingWei-Li/docsify/issues/4) ([#5](https://github.com/QingWei-Li/docsify/issues/5)) ([37e7984](https://github.com/QingWei-Li/docsify/commit/37e7984))



<a name="0.3.1"></a>
## [0.3.1](https://github.com/QingWei-Li/docsify/compare/v0.3.0...v0.3.1) (2016-11-27)



<a name="0.3.0"></a>
# [0.3.0](https://github.com/QingWei-Li/docsify/compare/v0.2.1...v0.3.0) (2016-11-27)



<a name="0.2.1"></a>
## [0.2.1](https://github.com/QingWei-Li/docsify/compare/v0.2.0...v0.2.1) (2016-11-26)



<a name="0.2.0"></a>
# [0.2.0](https://github.com/QingWei-Li/docsify/compare/v0.1.0...v0.2.0) (2016-11-26)



<a name="0.1.0"></a>
# [0.1.0](https://github.com/QingWei-Li/docsify/compare/v0.0.5...v0.1.0) (2016-11-26)



<a name="0.0.5"></a>
## [0.0.5](https://github.com/QingWei-Li/docsify/compare/v0.0.4...v0.0.5) (2016-11-24)



<a name="0.0.4"></a>
## [0.0.4](https://github.com/QingWei-Li/docsify/compare/v0.0.3...v0.0.4) (2016-11-22)



<a name="0.0.3"></a>
## [0.0.3](https://github.com/QingWei-Li/docsify/compare/v0.0.2...v0.0.3) (2016-11-20)



<a name="0.0.2"></a>
## [0.0.2](https://github.com/QingWei-Li/docsify/compare/v0.0.1...v0.0.2) (2016-11-20)



<a name="0.0.1"></a>
## 0.0.1 (2016-11-20)
