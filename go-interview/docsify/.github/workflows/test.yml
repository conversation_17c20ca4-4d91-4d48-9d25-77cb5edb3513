name: Build & Test

on:
  push:
    branches: [master, develop]
  pull_request:
    branches: [master, develop]

jobs:
  lint:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: ['lts/*']
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      - name: Install dependencies
        run: npm ci --ignore-scripts
      - name: Build
        run: npm run build
      - name: Lint
        run: npm run lint

  test-jest:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        node-version: ['lts/*']
        os: ['macos-latest', 'ubuntu-latest', 'windows-latest']
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      - name: Install dependencies
        run: npm ci --ignore-scripts
      - name: Build
        run: npm run build
      - name: Unit Tests
        run: npm run test:unit -- --ci --runInBand
      - name: Integration Tests
        run: npm run test:integration -- --ci --runInBand

  test-playwright:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: ['lts/*']
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      - name: Install dependencies
        run: npm ci --ignore-scripts
      - name: Build
        run: npm run build
      - name: Install Playwright
        run: npx playwright install --with-deps
      - name: E2E Tests (Playwright)
        run: npm run test:e2e
      - name: Store artifacts
        uses: actions/upload-artifact@v2
        if: failure()
        with:
          name: ${{ matrix.os }}-${{ matrix.node-version }}-artifacts
          path: |
            _playwright-results/
            _playwright-report/
