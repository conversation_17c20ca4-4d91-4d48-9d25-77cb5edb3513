@import url('https://fonts.googleapis.com/css?family=Roboto+Mono|Source+Sans+Pro:300,400,600')

$color-primary = #42b983
$color-bg = #fff
$color-text = #34495e
$sidebar-width = 280px

@import 'basic/_layout'
@import 'basic/_coverpage'

body
  background-color $color-bg

/* catalog */
.catalog
  background-color $color-bg
  color #364149

  li
    margin 6px 0 6px 0
    padding: 0 15px;

  ul li a
    color #505d6b
    font-size 14px
    font-weight normal
    overflow hidden
    text-decoration none
    text-overflow ellipsis
    white-space nowrap

    &:hover
      text-decoration underline

  ul li ul
    padding 0

  ul li.active > a
    color $color-primary
    color var(--theme-color, $color-primary)
    position: relative;
    &::before
        content ''
        position absolute
        left 0
        top 0
        bottom 0
        width 2px
        background-color $color-primary
        background-color var(--theme-color, $color-primary)

/* sidebar */
.sidebar
  background-color $color-bg
  color #364149

  li
    margin 6px 0 6px 0

  ul li a
    color #505d6b
    font-size 14px
    font-weight normal
    overflow hidden
    text-decoration none
    text-overflow ellipsis
    white-space nowrap

    &:hover
      text-decoration underline

  ul li ul
    padding 0

  ul li.active > a
    color $color-primary
    color var(--theme-color, $color-primary)
    position: relative;
    &::before
        content ''
        position absolute
        left 0
        top 0
        bottom 0
        width 2px
        background-color $color-primary
        background-color var(--theme-color, $color-primary)
.app-sub-sidebar
  li
    &::before
      content '-'
      padding-right 4px
      float left

/* markdown content found on pages */
.markdown-section h1, .markdown-section h2, .markdown-section h3, .markdown-section h4, .markdown-section strong
  color #2c3e50
  font-weight 600

.markdown-section a
  color $color-primary
  color var(--theme-color, $color-primary)
  font-weight 600

.markdown-section h1
  font-size 2rem
  margin 0 0 1rem

.markdown-section h2
  font-size 1.75rem
  margin 45px 0 0.8rem

.markdown-section h3
  font-size 1.5rem
  margin 40px 0 0.6rem

.markdown-section h4
  font-size 1.25rem

.markdown-section h5
  font-size 1rem

.markdown-section h6
  color #777
  font-size 1rem

.markdown-section figure, .markdown-section p
  margin 1.2em 0

.markdown-section p, .markdown-section ul, .markdown-section ol
  line-height 1.6rem
  word-spacing 0.05rem

.markdown-section ul, .markdown-section ol
  padding-left 1.5rem

.markdown-section blockquote
  border-left 4px solid $color-primary
  border-left 4px solid var(--theme-color, $color-primary)
  color #858585
  margin 2em 0
  padding-left 20px

.markdown-section blockquote p
  font-weight 600
  margin-left 0

.markdown-section iframe
  margin 1em 0

.markdown-section em
  color #7f8c8d

.markdown-section code,
.markdown-section pre,
.markdown-section output::after
  font-family 'Roboto Mono', Monaco, courier, monospace

.markdown-section code,
.markdown-section pre
  background-color #f8f8f8

.markdown-section pre,
.markdown-section output
  margin 1.2em 0
  position relative

.markdown-section pre > code,
.markdown-section output
  border-radius 2px
  display block

.markdown-section pre > code,
.markdown-section output::after
  -moz-osx-font-smoothing initial
  -webkit-font-smoothing initial

.markdown-section code
  border-radius 2px
  color #e96900
  margin 0 2px
  padding 3px 5px
  white-space pre-wrap

.markdown-section > :not(h1):not(h2):not(h3):not(h4):not(h5):not(h6) code
  font-size 0.8rem

.markdown-section pre
  padding 0 1.4rem
  line-height 1.5rem
  overflow auto
  word-wrap normal

.markdown-section pre > code
  color #525252
  font-size 0.8rem
  padding 2.2em 5px
  line-height inherit
  margin 0 2px
  max-width inherit
  overflow inherit
  white-space inherit

.markdown-section output
  padding: 1.7rem 1.4rem
  border 1px dotted #ccc

.markdown-section output > :first-child
  margin-top: 0;

.markdown-section output > :last-child
  margin-bottom: 0;

.markdown-section code::after, .markdown-section code::before,
.markdown-section output::after, .markdown-section output::before
  letter-spacing 0.05rem

.markdown-section pre::after,
.markdown-section output::after
  color #ccc
  font-size 0.6rem
  font-weight 600
  height 15px
  line-height 15px
  padding 5px 10px 0
  position absolute
  right 0
  text-align right
  top 0

.markdown-section pre::after
.markdown-section output::after
  content attr(data-lang)

/* code highlight */
.token.comment, .token.prolog, .token.doctype, .token.cdata
  color #8e908c

.token.namespace
  opacity 0.7

.token.boolean, .token.number
  color #c76b29

.token.punctuation
  color #525252

.token.property
  color #c08b30

.token.tag
  color #2973b7

.token.string
  color $color-primary
  color var(--theme-color, $color-primary)

.token.selector
  color #6679cc

.token.attr-name
  color #2973b7

.token.entity, .token.url, .language-css .token.string, .style .token.string
  color #22a2c9

.token.attr-value, .token.control, .token.directive, .token.unit
  color $color-primary
  color var(--theme-color, $color-primary)

.token.keyword, .token.function
  color #e96900

.token.statement, .token.regex, .token.atrule
  color #22a2c9

.token.placeholder, .token.variable
  color #3d8fd1

.token.deleted
  text-decoration line-through

.token.inserted
  border-bottom 1px dotted #202746
  text-decoration none

.token.italic
  font-style italic

.token.important, .token.bold
  font-weight bold

.token.important
  color #c94922

.token.entity
  cursor help

code .token
  -moz-osx-font-smoothing initial
  -webkit-font-smoothing initial
  min-height 1.5rem
  position: relative
  left: auto

/* github corner */
.github-corner
  top 48px
  svg
    color $color-bg
    fill unset

/* sidebar */
.sidebar-nav
  strong
    font-weight 400

/* selected color */
::selection
    background: #d9d9d9cc;
    color: #ff3300

::-moz-selection
    background: #d9d9d9cc;
    color: #ff3300

::-webkit-selection
    background: #d9d9d9cc;
    color: #ff3300

:root
  --theme-color #007d9c