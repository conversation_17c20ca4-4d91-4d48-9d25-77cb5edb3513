*
  -webkit-font-smoothing antialiased
  -webkit-overflow-scrolling touch
  -webkit-tap-highlight-color rgba(0, 0, 0, 0)
  -webkit-text-size-adjust none
  -webkit-touch-callout none
  box-sizing border-box

body:not(.ready)
  overflow hidden

  [data-cloak], .app-nav, > nav
    display none

div#app
  font-size 30px
  font-weight lighter
  margin 40vh auto
  text-align center

  &:empty::before
    content 'Loading...'

img.emoji
  height 1.2em
  vertical-align middle

span.emoji
  font-family "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"
  font-size 1.2em
  vertical-align middle

.progress
  background-color $color-primary
  background-color var(--theme-color, $color-primary)
  height 2px
  left 0px
  position fixed
  right 0px
  top 0px
  transition width 0.2s, opacity 0.4s
  width 0%
  z-index 999999

.search a:hover
  color $color-primary
  color var(--theme-color, $color-primary)

.search .search-keyword
  color $color-primary
  color var(--theme-color, $color-primary)
  font-style normal
  font-weight bold

html, body
  height 100%

body
  -moz-osx-font-smoothing grayscale
  -webkit-font-smoothing antialiased
  color $color-text
  font-family 'Source Sans Pro', 'Helvetica Neue', Arial, sans-serif
  font-size 15px
  letter-spacing 0
  margin 0
  overflow-x hidden

img
  max-width 100%

a[disabled]
  cursor not-allowed
  opacity 0.6

kbd
  border solid 1px #ccc
  border-radius 3px
  display inline-block
  font-size 12px !important
  line-height 12px
  margin-bottom 3px
  padding 3px 5px
  vertical-align middle

li input[type='checkbox']
  margin 0 0.2em 0.25em 0
  vertical-align middle

/* navbar */
.app-nav
  margin 25px 60px 0 0
  position absolute
  right 0
  text-align right
  z-index 10

  &.no-badge
    margin-right 25px

  p
    margin 0

  > a
    margin 0 1rem
    padding 5px 0

  ul, li
    display inline-block
    list-style none
    margin 0

  a
    color inherit
    text-decoration none
    transition color 0.3s

    &:hover
      color $color-primary
      color var(--theme-color, $color-primary)

    &.active
      border-bottom 2px solid $color-primary
      border-bottom 2px solid var(--theme-color, $color-primary)
      color $color-primary
      color var(--theme-color, $color-primary)

  /* navbar dropdown */
  li
    display inline-block
    margin 0 1rem
    padding 5px 0
    position relative
    cursor pointer

    ul
      background-color #fff
      border 1px solid #ddd
      border-bottom-color #ccc
      border-radius 4px
      box-sizing border-box
      display none
      max-height calc(100vh - 61px)
      overflow-y auto
      padding 10px 0
      position absolute
      right -15px
      text-align left
      top 100%
      white-space nowrap

      li
        display block
        font-size 14px
        line-height 1rem
        margin 0
        margin 8px 14px
        white-space nowrap

      a
        display block
        font-size inherit
        margin 0
        padding 0

        &.active
          border-bottom 0

    &:hover ul
      display block

/* github corner */
.github-corner
  border-bottom 0
  position fixed
  right 0
  text-decoration none
  top 40px
  z-index 9

  &:hover .octo-arm
    animation octocat-wave 560ms ease-in-out

  svg
    color $color-bg
    fill $color-primary
    fill var(--theme-color, $color-primary)
    height 80px
    width 80px

/* main */
main
  display block
  position relative
  width 100vw
  height 100%
  z-index 0

main.hidden
  display none

.anchor
  display inline-block
  text-decoration none
  transition all 0.3s

  span
    color $color-text

  &:hover
    text-decoration underline

/* catalog */
.catalog
  border-right 1px solid rgba(0, 0, 0, 0.07)
  overflow-y auto
  padding 60px 0 0
  position absolute
  top 0
  bottom 0
  left 0
  transition transform 250ms ease-out
  width $sidebar-width
  z-index 20

  > h1
    margin 0 auto 1rem
    font-size 1.5rem
    font-weight 300
    text-align center

    a
      color inherit
      text-decoration none

    .app-nav
      display block
      position static

  .catalog-nav
    line-height 2em
    padding-bottom 40px

    > p 
      text-align center

  li.collapse
    .app-sub-sidebar
      display none

  ul
    margin 0
    padding 0

  li > p
    font-weight 700
    margin 0

  ul, ul li
    list-style none

  ul li a
    border-bottom none
    display block
    padding 0 15px

  ul li ul
    padding-left 20px

  a
    color inherit
    text-decoration none
    transition color 0.3s

    &:hover
      color $color-primary
      color var(--theme-color, $color-primary)

    &.active
      color $color-primary
      color var(--theme-color, $color-primary)

  &::-webkit-scrollbar
    width 4px

  &::-webkit-scrollbar-thumb
    background transparent
    border-radius 4px

  &:hover::-webkit-scrollbar-thumb
    background rgba(136, 136, 136, 0.4)

  &:hover::-webkit-scrollbar-track
    background rgba(136, 136, 136, 0.1)

/* sidebar */
.sidebar
  border-left 1px solid rgba(0, 0, 0, 0.07)
  overflow-y auto
  padding 60px 0 0
  position absolute
  top 0
  bottom 0
  right 0
  transition transform 250ms ease-out
  width $sidebar-width
  z-index 20

  > h1
    margin 0 auto 1rem
    font-size 1.5rem
    font-weight 300
    text-align center

    a
      color inherit
      text-decoration none

    .app-nav
      display block
      position static

  .sidebar-nav
    line-height 2em
    padding-bottom 40px
  
  nav
    padding 0px 0px 0px 6px

  li.collapse
    .app-sub-sidebar
      display none

  ul
    margin 0
    padding 0

  li > p
    font-weight 700
    margin 0

  ul, ul li
    list-style none

  ul li a
    border-bottom none
    display block
    padding 0 15px

  ul li ul
    padding-left 20px

  &::-webkit-scrollbar
    width 4px

  &::-webkit-scrollbar-thumb
    background transparent
    border-radius 4px

  &:hover::-webkit-scrollbar-thumb
    background rgba(136, 136, 136, 0.4)

  &:hover::-webkit-scrollbar-track
    background rgba(136, 136, 136, 0.1)

/* sidebar toggle */
.sidebar-toggle
  background-color transparent
  background-color rgba($color-bg, 0.8)
  border 0
  outline none
  padding 10px
  position absolute
  bottom 0
  right 0
  text-align center
  transition opacity 0.3s
  z-index 30
  cursor pointer

  &:hover .sidebar-toggle-button
    opacity 0.4

  span
    background-color $color-primary
    background-color var(--theme-color, $color-primary)
    display block
    margin-bottom 4px
    width 16px
    height 2px

body.sticky
  .sidebar, .catalog, .sidebar-toggle
    position fixed

/* main content */
.content
  padding-top 60px
  position absolute
  top 0
  left $sidebar-width
  bottom 0
  right $sidebar-width
  transition left 250ms ease

/* markdown content found on pages */
.markdown-section
  margin 0 auto
  padding 30px 15px 40px 15px
  position relative
  max-width 80%
  
  > *
    box-sizing border-box
    font-size inherit

  > :first-child
    margin-top 0 !important

.markdown-section hr
  border none
  border-bottom 1px solid #eee
  margin 2em 0

.markdown-section iframe
  border 1px solid #eee
  /* fix horizontal overflow on iOS Safari */
  width 1px
  min-width 100%

.markdown-section table
  border-collapse collapse
  border-spacing 0
  display block
  margin-bottom 1rem
  overflow auto
  width 100%

.markdown-section th
  border 1px solid #ddd
  font-weight bold
  padding 6px 13px

.markdown-section td
  border 1px solid #ddd
  padding 6px 13px

.markdown-section tr
  border-top 1px solid #ccc

  &:nth-child(2n)
    background-color #f8f8f8

.markdown-section p.tip
  background-color #f8f8f8
  border-bottom-right-radius 2px
  border-left 4px solid #f66
  border-top-right-radius 2px
  margin 2em 0
  padding 12px 24px 12px 30px
  position relative

  &:before
    background-color #f66
    border-radius 100%
    color $color-bg
    content '!'
    font-family 'Dosis', 'Source Sans Pro', 'Helvetica Neue', Arial, sans-serif
    font-size 14px
    font-weight bold
    left -12px
    line-height 20px
    position absolute
    height 20px
    width 20px
    text-align center
    top 14px

  code
    background-color #efefef

  em
    color $color-text

.markdown-section p.warn
  background rgba($color-primary, 0.1)
  border-radius 2px
  padding 1rem

.markdown-section ul.task-list > li
  list-style-type none

body.close
  .sidebar
    transform translateX($sidebar-width)

  .sidebar-toggle
    width auto

  .content
    right 0

@media print
  .github-corner, .sidebar-toggle, .sidebar, .app-nav
    display none

@media screen and (max-width: 768px)
  .github-corner, .sidebar-toggle, .sidebar
    position fixed

  .app-nav
    margin-top 16px

  .app-nav li ul
    top 30px

  main
    height auto
    min-height 100vh
    overflow-x hidden

  .sidebar
    right - $sidebar-width
    transition transform 250ms ease-out

  .content
    left 0
    max-width 100vw
    position static
    padding-top 20px
    transition transform 250ms ease

  .app-nav, .github-corner
    transition transform 250ms ease-out

  .sidebar-toggle
    background-color transparent
    width auto

  body.close
    .sidebar
      transform translateX(- $sidebar-width)

    .sidebar-toggle
      background-color rgba($color-bg, 0.8)
      transition 1s background-color
      padding 10px

    .content
      transform translateX(0)

    .app-nav, .github-corner
      display none

  .github-corner
    &:hover .octo-arm
      animation none

    .octo-arm
      animation octocat-wave 560ms ease-in-out

@keyframes octocat-wave
  0%, 100%
    transform rotate(0)

  20%, 60%
    transform rotate(-25deg)

  40%, 80%
    transform rotate(10deg)
