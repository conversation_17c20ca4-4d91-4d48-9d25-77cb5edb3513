## 技术文档集合

这份文档集合涵盖了我在多个技术领域的学习和实践过程中整理的核心知识点。主要内容包括以下几个方面：

* Go
  * [chan底层原理](docs%2FGo%2Fchan%E5%BA%95%E5%B1%82%E5%8E%9F%E7%90%86.md)
  * [Context的使用场景](docs%2FGo%2FContext%E7%9A%84%E4%BD%BF%E7%94%A8%E5%9C%BA%E6%99%AF.md)
  * [CSP并发模型](docs%2FGo%2FCSP%E5%B9%B6%E5%8F%91%E6%A8%A1%E5%9E%8B.md)
  * [dlv分析golang进程](docs%2FGo%2Fdlv%E5%88%86%E6%9E%90golang%E8%BF%9B%E7%A8%8B.md)
  * [GC 是怎样监听你的应用的](docs%2FGo%2FGC%20%E6%98%AF%E6%80%8E%E6%A0%B7%E7%9B%91%E5%90%AC%E4%BD%A0%E7%9A%84%E5%BA%94%E7%94%A8%E7%9A%84.md)
  * [GC垃圾回收算法](docs%2FGo%2FGC%E5%9E%83%E5%9C%BE%E5%9B%9E%E6%94%B6%E7%AE%97%E6%B3%95.md)
  * [go runtime 简析](docs%2FGo%2Fgo%20runtime%20%E7%AE%80%E6%9E%90.md)
  * [gofunc过程](docs%2FGo%2Fgofunc%E8%BF%87%E7%A8%8B.md)
  * [gopark函数和goready函数原理分析](docs%2FGo%2Fgopark%E5%87%BD%E6%95%B0%E5%92%8Cgoready%E5%87%BD%E6%95%B0%E5%8E%9F%E7%90%86%E5%88%86%E6%9E%90.md)
  * [Go协程的栈内存管理](docs%2FGo%2FGo%E5%8D%8F%E7%A8%8B%E7%9A%84%E6%A0%88%E5%86%85%E5%AD%98%E7%AE%A1%E7%90%86.md)
  * [GPM调度模型](docs%2FGo%2FGPM%E8%B0%83%E5%BA%A6%E6%A8%A1%E5%9E%8B.md)
  * [HTTP Client大量长连接保持](docs%2FGo%2FHTTP%20Client%E5%A4%A7%E9%87%8F%E9%95%BF%E8%BF%9E%E6%8E%A5%E4%BF%9D%E6%8C%81.md)
  * [Interface内部实现的理解](docs%2FGo%2FInterface%E5%86%85%E9%83%A8%E5%AE%9E%E7%8E%B0%E7%9A%84%E7%90%86%E8%A7%A3.md)
  * [mutex怎么使用，乐观和悲观锁的实现](docs%2FGo%2Fmutex%E6%80%8E%E4%B9%88%E4%BD%BF%E7%94%A8%EF%BC%8C%E4%B9%90%E8%A7%82%E5%92%8C%E6%82%B2%E8%A7%82%E9%94%81%E7%9A%84%E5%AE%9E%E7%8E%B0.md)
  * [slice实践以及底层实现](docs%2FGo%2Fslice%E5%AE%9E%E8%B7%B5%E4%BB%A5%E5%8F%8A%E5%BA%95%E5%B1%82%E5%AE%9E%E7%8E%B0.md)
  * [Stop the World (STW)](docs%2FGo%2FStop%20the%20World%20(STW).md)
  * [sync.Pool](docs%2FGo%2Fsync.Pool.md)
  * [乐观锁与悲观锁与Golang](docs%2FGo%2F%E4%B9%90%E8%A7%82%E9%94%81%E4%B8%8E%E6%82%B2%E8%A7%82%E9%94%81%E4%B8%8EGolang.md)
  * [互斥锁实现原理剖析](docs%2FGo%2F%E4%BA%92%E6%96%A5%E9%94%81%E5%AE%9E%E7%8E%B0%E5%8E%9F%E7%90%86%E5%89%96%E6%9E%90.md)
  * [内存泄露的发现与排查](docs%2FGo%2F%E5%86%85%E5%AD%98%E6%B3%84%E9%9C%B2%E7%9A%84%E5%8F%91%E7%8E%B0%E4%B8%8E%E6%8E%92%E6%9F%A5.md)
  * [如何回收goroutine](docs%2FGo%2F%E5%A6%82%E4%BD%95%E5%9B%9E%E6%94%B6goroutine.md)
  * [常用包](docs%2FGo%2F%E5%B8%B8%E7%94%A8%E5%8C%85.md)
  * [特权 Goroutine g0](docs%2FGo%2F%E7%89%B9%E6%9D%83%20Goroutine%20g0.md)
  * [线程的实现模型](docs%2FGo%2F%E7%BA%BF%E7%A8%8B%E7%9A%84%E5%AE%9E%E7%8E%B0%E6%A8%A1%E5%9E%8B.md)
  * [结构体是否可以比较](docs%2FGo%2F%E7%BB%93%E6%9E%84%E4%BD%93%E6%98%AF%E5%90%A6%E5%8F%AF%E4%BB%A5%E6%AF%94%E8%BE%83.md)
  * [详解通信数据协议ProtoBuf](docs%2FGo%2F%E8%AF%A6%E8%A7%A3%E9%80%9A%E4%BF%A1%E6%95%B0%E6%8D%AE%E5%8D%8F%E8%AE%AEProtoBuf.md)
  * [读写分离 sync.Map](docs%2FGo%2F%E8%AF%BB%E5%86%99%E5%88%86%E7%A6%BB%20sync.Map.md)
  * [读写锁的实现及底层原理](docs%2FGo%2F%E8%AF%BB%E5%86%99%E9%94%81%E7%9A%84%E5%AE%9E%E7%8E%B0%E5%8F%8A%E5%BA%95%E5%B1%82%E5%8E%9F%E7%90%86.md)
  * [长连接和短连接的学习](docs%2FGo%2F%E9%95%BF%E8%BF%9E%E6%8E%A5%E5%92%8C%E7%9F%AD%E8%BF%9E%E6%8E%A5%E7%9A%84%E5%AD%A6%E4%B9%A0.md)
* Kafka
  * [Kafka事务](docs%2FKafka%2FKafka%E4%BA%8B%E5%8A%A1.md)
  * [Kafka高效率原因](docs%2FKafka%2FKafka%E9%AB%98%E6%95%88%E7%8E%87%E5%8E%9F%E5%9B%A0.md)
  * [为什么要使用消息队列 ](docs%2FKafka%2F%E4%B8%BA%E4%BB%80%E4%B9%88%E8%A6%81%E4%BD%BF%E7%94%A8%E6%B6%88%E6%81%AF%E9%98%9F%E5%88%97%20.md)
  * [如何保证高可用](docs%2FKafka%2F%E5%A6%82%E4%BD%95%E4%BF%9D%E8%AF%81%E9%AB%98%E5%8F%AF%E7%94%A8.md)
  * [架构原理及存储机制](docs%2FKafka%2F%E6%9E%B6%E6%9E%84%E5%8E%9F%E7%90%86%E5%8F%8A%E5%AD%98%E5%82%A8%E6%9C%BA%E5%88%B6.md)
  * [消费者策略、Rebalance机制、Offset存储机制](docs%2FKafka%2F%E6%B6%88%E8%B4%B9%E8%80%85%E7%AD%96%E7%95%A5%E3%80%81Rebalance%E6%9C%BA%E5%88%B6%E3%80%81Offset%E5%AD%98%E5%82%A8%E6%9C%BA%E5%88%B6.md)
* Mysql
  * [binlog、redo log和undo log](docs%2FMysql%2Fbinlog%E3%80%81redo%20log%E5%92%8Cundo%20log.md)
  * [b树与b+树的区别](docs%2FMysql%2Fb%E6%A0%91%E4%B8%8Eb%2B%E6%A0%91%E7%9A%84%E5%8C%BA%E5%88%AB.md)
  * [MongoDB和MySQL的区别](docs%2FMysql%2FMongoDB%E5%92%8CMySQL%E7%9A%84%E5%8C%BA%E5%88%AB.md)
  * [MVVC原理](docs%2FMysql%2FMVVC%E5%8E%9F%E7%90%86.md)
  * [MyISAM和InnoDB](docs%2FMysql%2FMyISAM%E5%92%8CInnoDB.md)
  * [MySQL 主从复制原理](docs%2FMysql%2FMySQL%20%E4%B8%BB%E4%BB%8E%E5%A4%8D%E5%88%B6%E5%8E%9F%E7%90%86.md)
  * [MySQL中高性能索引的策略](docs%2FMysql%2FMySQL%E4%B8%AD%E9%AB%98%E6%80%A7%E8%83%BD%E7%B4%A2%E5%BC%95%E7%9A%84%E7%AD%96%E7%95%A5.md)
  * [MySQL主从复制](docs%2FMysql%2FMySQL%E4%B8%BB%E4%BB%8E%E5%A4%8D%E5%88%B6.md)
  * [sql语句性能分析](docs%2FMysql%2Fsql%E8%AF%AD%E5%8F%A5%E6%80%A7%E8%83%BD%E5%88%86%E6%9E%90.md)
  * [为什么MySQL数据库索引选择使用B+树](docs%2FMysql%2F%E4%B8%BA%E4%BB%80%E4%B9%88MySQL%E6%95%B0%E6%8D%AE%E5%BA%93%E7%B4%A2%E5%BC%95%E9%80%89%E6%8B%A9%E4%BD%BF%E7%94%A8B%2B%E6%A0%91.md)
  * [主键索引和非主键索引的区别](docs%2FMysql%2F%E4%B8%BB%E9%94%AE%E7%B4%A2%E5%BC%95%E5%92%8C%E9%9D%9E%E4%B8%BB%E9%94%AE%E7%B4%A2%E5%BC%95%E7%9A%84%E5%8C%BA%E5%88%AB.md)
  * [二阶段提交](docs%2FMysql%2F%E4%BA%8C%E9%98%B6%E6%AE%B5%E6%8F%90%E4%BA%A4.md)
  * [什么情况下会导致索引失效](docs%2FMysql%2F%E4%BB%80%E4%B9%88%E6%83%85%E5%86%B5%E4%B8%8B%E4%BC%9A%E5%AF%BC%E8%87%B4%E7%B4%A2%E5%BC%95%E5%A4%B1%E6%95%88.md)
  * [什么情况下建立索引，哪些情况不行](docs%2FMysql%2F%E4%BB%80%E4%B9%88%E6%83%85%E5%86%B5%E4%B8%8B%E5%BB%BA%E7%AB%8B%E7%B4%A2%E5%BC%95%EF%BC%8C%E5%93%AA%E4%BA%9B%E6%83%85%E5%86%B5%E4%B8%8D%E8%A1%8C.md)
  * [关于mysql索引最左匹配原则的理解](docs%2FMysql%2F%E5%85%B3%E4%BA%8Emysql%E7%B4%A2%E5%BC%95%E6%9C%80%E5%B7%A6%E5%8C%B9%E9%85%8D%E5%8E%9F%E5%88%99%E7%9A%84%E7%90%86%E8%A7%A3.md)
  * [四种事务隔离级别](docs%2FMysql%2F%E5%9B%9B%E7%A7%8D%E4%BA%8B%E5%8A%A1%E9%9A%94%E7%A6%BB%E7%BA%A7%E5%88%AB.md)
  * [垂直分表和水平分表和分表的跨表查询 ](docs%2FMysql%2F%E5%9E%82%E7%9B%B4%E5%88%86%E8%A1%A8%E5%92%8C%E6%B0%B4%E5%B9%B3%E5%88%86%E8%A1%A8%E5%92%8C%E5%88%86%E8%A1%A8%E7%9A%84%E8%B7%A8%E8%A1%A8%E6%9F%A5%E8%AF%A2%20.md)
  * [如何解决幻读的](docs%2FMysql%2F%E5%A6%82%E4%BD%95%E8%A7%A3%E5%86%B3%E5%B9%BB%E8%AF%BB%E7%9A%84.md)
  * [当前读与快照读](docs%2FMysql%2F%E5%BD%93%E5%89%8D%E8%AF%BB%E4%B8%8E%E5%BF%AB%E7%85%A7%E8%AF%BB.md)
  * [悲观锁与乐观锁区别及使用场景](docs%2FMysql%2F%E6%82%B2%E8%A7%82%E9%94%81%E4%B8%8E%E4%B9%90%E8%A7%82%E9%94%81%E5%8C%BA%E5%88%AB%E5%8F%8A%E4%BD%BF%E7%94%A8%E5%9C%BA%E6%99%AF.md)
  * [数据库三范式](docs%2FMysql%2F%E6%95%B0%E6%8D%AE%E5%BA%93%E4%B8%89%E8%8C%83%E5%BC%8F.md)
  * [数据库的垂直切分与水平切分](docs%2FMysql%2F%E6%95%B0%E6%8D%AE%E5%BA%93%E7%9A%84%E5%9E%82%E7%9B%B4%E5%88%87%E5%88%86%E4%B8%8E%E6%B0%B4%E5%B9%B3%E5%88%87%E5%88%86.md)
  * [百万到千万级别数据量的优化方案](docs%2FMysql%2F%E7%99%BE%E4%B8%87%E5%88%B0%E5%8D%83%E4%B8%87%E7%BA%A7%E5%88%AB%E6%95%B0%E6%8D%AE%E9%87%8F%E7%9A%84%E4%BC%98%E5%8C%96%E6%96%B9%E6%A1%88.md)
  * [索引失效的几种情况](docs%2FMysql%2F%E7%B4%A2%E5%BC%95%E5%A4%B1%E6%95%88%E7%9A%84%E5%87%A0%E7%A7%8D%E6%83%85%E5%86%B5.md)
  * [联合索引](docs%2FMysql%2F%E8%81%94%E5%90%88%E7%B4%A2%E5%BC%95.md)
  * [聚簇索引和非聚簇索引](docs%2FMysql%2F%E8%81%9A%E7%B0%87%E7%B4%A2%E5%BC%95%E5%92%8C%E9%9D%9E%E8%81%9A%E7%B0%87%E7%B4%A2%E5%BC%95.md)
  * [表锁和行锁机制](docs%2FMysql%2F%E8%A1%A8%E9%94%81%E5%92%8C%E8%A1%8C%E9%94%81%E6%9C%BA%E5%88%B6.md)
  * [记录锁、间隙锁与临键锁](docs%2FMysql%2F%E8%AE%B0%E5%BD%95%E9%94%81%E3%80%81%E9%97%B4%E9%9A%99%E9%94%81%E4%B8%8E%E4%B8%B4%E9%94%AE%E9%94%81.md)
  * [说说脏读、不可重复读、幻读，怎么解决的](docs%2FMysql%2F%E8%AF%B4%E8%AF%B4%E8%84%8F%E8%AF%BB%E3%80%81%E4%B8%8D%E5%8F%AF%E9%87%8D%E5%A4%8D%E8%AF%BB%E3%80%81%E5%B9%BB%E8%AF%BB%EF%BC%8C%E6%80%8E%E4%B9%88%E8%A7%A3%E5%86%B3%E7%9A%84.md)
  * [读写分离，发现读不到数据](docs%2FMysql%2F%E8%AF%BB%E5%86%99%E5%88%86%E7%A6%BB%EF%BC%8C%E5%8F%91%E7%8E%B0%E8%AF%BB%E4%B8%8D%E5%88%B0%E6%95%B0%E6%8D%AE.md)
  * [高并发下怎么做余额扣减](docs%2FMysql%2F%E9%AB%98%E5%B9%B6%E5%8F%91%E4%B8%8B%E6%80%8E%E4%B9%88%E5%81%9A%E4%BD%99%E9%A2%9D%E6%89%A3%E5%87%8F.md)
* Network
  * [ARP协议工作原理](docs%2FNetwork%2FARP%E5%8D%8F%E8%AE%AE%E5%B7%A5%E4%BD%9C%E5%8E%9F%E7%90%86.md)
  * [HTTP 1.0，1.1，2.0 的区别](docs%2FNetwork%2FHTTP%201.0%EF%BC%8C1.1%EF%BC%8C2.0%20%E7%9A%84%E5%8C%BA%E5%88%AB.md)
  * [HTTP 与 HTTPS 区别](docs%2FNetwork%2FHTTP%20%E4%B8%8E%20HTTPS%20%E5%8C%BA%E5%88%AB.md)
  * [HTTP 的方法有哪些](docs%2FNetwork%2FHTTP%20%E7%9A%84%E6%96%B9%E6%B3%95%E6%9C%89%E5%93%AA%E4%BA%9B.md)
  * [HTTP协议中的OPTIONS](docs%2FNetwork%2FHTTP%E5%8D%8F%E8%AE%AE%E4%B8%AD%E7%9A%84OPTIONS.md)
  * [HTTP状态码](docs%2FNetwork%2FHTTP%E7%8A%B6%E6%80%81%E7%A0%81.md)
  * [HTTP的结构](docs%2FNetwork%2FHTTP%E7%9A%84%E7%BB%93%E6%9E%84.md)
  * [Linux端口范围](docs%2FNetwork%2FLinux%E7%AB%AF%E5%8F%A3%E8%8C%83%E5%9B%B4.md)
  * [OSI 七层模型与 TCP&IP 五层模型](docs%2FNetwork%2FOSI%20%E4%B8%83%E5%B1%82%E6%A8%A1%E5%9E%8B%E4%B8%8E%20TCP%26IP%20%E4%BA%94%E5%B1%82%E6%A8%A1%E5%9E%8B.md)
  * [ping协议](docs%2FNetwork%2Fping%E5%8D%8F%E8%AE%AE.md)
  * [RestFul 与 RPC 的区别](docs%2FNetwork%2FRestFul%20%E4%B8%8E%20RPC%20%E7%9A%84%E5%8C%BA%E5%88%AB.md)
  * [TCP 三次握手以及四次挥手的流程](docs%2FNetwork%2FTCP%20%E4%B8%89%E6%AC%A1%E6%8F%A1%E6%89%8B%E4%BB%A5%E5%8F%8A%E5%9B%9B%E6%AC%A1%E6%8C%A5%E6%89%8B%E7%9A%84%E6%B5%81%E7%A8%8B.md)
  * [TCP 中常见的拥塞控制算法有哪些](docs%2FNetwork%2FTCP%20%E4%B8%AD%E5%B8%B8%E8%A7%81%E7%9A%84%E6%8B%A5%E5%A1%9E%E6%8E%A7%E5%88%B6%E7%AE%97%E6%B3%95%E6%9C%89%E5%93%AA%E4%BA%9B.md)
  * [TCP 半连接发生场景](docs%2FNetwork%2FTCP%20%E5%8D%8A%E8%BF%9E%E6%8E%A5%E5%8F%91%E7%94%9F%E5%9C%BA%E6%99%AF.md)
  * [TCP 协议的延迟 ACK 和累计应答](docs%2FNetwork%2FTCP%20%E5%8D%8F%E8%AE%AE%E7%9A%84%E5%BB%B6%E8%BF%9F%20ACK%20%E5%92%8C%E7%B4%AF%E8%AE%A1%E5%BA%94%E7%AD%94.md)
  * [TCP 可靠传输的保证](docs%2FNetwork%2FTCP%20%E5%8F%AF%E9%9D%A0%E4%BC%A0%E8%BE%93%E7%9A%84%E4%BF%9D%E8%AF%81.md)
  * [TCP 滑动窗口以及重传机制](docs%2FNetwork%2FTCP%20%E6%BB%91%E5%8A%A8%E7%AA%97%E5%8F%A3%E4%BB%A5%E5%8F%8A%E9%87%8D%E4%BC%A0%E6%9C%BA%E5%88%B6.md)
  * [TCP 的 keepalive](docs%2FNetwork%2FTCP%20%E7%9A%84%20keepalive.md)
  * [TCP 的 TIME_WAIT](docs%2FNetwork%2FTCP%20%E7%9A%84%20TIME_WAIT.md)
  * [TCP 的报文头部结构](docs%2FNetwork%2FTCP%20%E7%9A%84%E6%8A%A5%E6%96%87%E5%A4%B4%E9%83%A8%E7%BB%93%E6%9E%84.md)
  * [tcp、udp的区别](docs%2FNetwork%2Ftcp%E3%80%81udp%E7%9A%84%E5%8C%BA%E5%88%AB.md)
  * [TCP粘包](docs%2FNetwork%2FTCP%E7%B2%98%E5%8C%85.md)
  * [udp之http3quic协议](docs%2FNetwork%2Fudp%E4%B9%8Bhttp3quic%E5%8D%8F%E8%AE%AE.md)
  * [一次 HTTP 的请求过程](docs%2FNetwork%2F%E4%B8%80%E6%AC%A1%20HTTP%20%E7%9A%84%E8%AF%B7%E6%B1%82%E8%BF%87%E7%A8%8B.md)
  * [了解网络攻击吗](docs%2FNetwork%2F%E4%BA%86%E8%A7%A3%E7%BD%91%E7%BB%9C%E6%94%BB%E5%87%BB%E5%90%97.md)
  * [什么是 SYN flood](docs%2FNetwork%2F%E4%BB%80%E4%B9%88%E6%98%AF%20SYN%20flood.md)
  * [从系统层面上，UDP如何保证尽量可靠](docs%2FNetwork%2F%E4%BB%8E%E7%B3%BB%E7%BB%9F%E5%B1%82%E9%9D%A2%E4%B8%8A%EF%BC%8CUDP%E5%A6%82%E4%BD%95%E4%BF%9D%E8%AF%81%E5%B0%BD%E9%87%8F%E5%8F%AF%E9%9D%A0.md)
  * [图解HTTPS](docs%2FNetwork%2F%E5%9B%BE%E8%A7%A3HTTPS.md)
  * [查看服务器是否被攻击的方法](docs%2FNetwork%2F%E6%9F%A5%E7%9C%8B%E6%9C%8D%E5%8A%A1%E5%99%A8%E6%98%AF%E5%90%A6%E8%A2%AB%E6%94%BB%E5%87%BB%E7%9A%84%E6%96%B9%E6%B3%95.md)
  * [网络七层协议](docs%2FNetwork%2F%E7%BD%91%E7%BB%9C%E4%B8%83%E5%B1%82%E5%8D%8F%E8%AE%AE.md)
  * [限流策略](docs%2FNetwork%2F%E9%99%90%E6%B5%81%E7%AD%96%E7%95%A5.md)
* Redis
  * [AOF和RDB的过期删除策略](docs%2FRedis%2FAOF%E5%92%8CRDB%E7%9A%84%E8%BF%87%E6%9C%9F%E5%88%A0%E9%99%A4%E7%AD%96%E7%95%A5.md)
  * [AOF文件太大怎么办](docs%2FRedis%2FAOF%E6%96%87%E4%BB%B6%E5%A4%AA%E5%A4%A7%E6%80%8E%E4%B9%88%E5%8A%9E.md)
  * [bgsave时数据拷贝过程](docs%2FRedis%2Fbgsave%E6%97%B6%E6%95%B0%E6%8D%AE%E6%8B%B7%E8%B4%9D%E8%BF%87%E7%A8%8B.md)
  * [pipeline](docs%2FRedis%2Fpipeline.md)
  * [Redis 哈希槽](docs%2FRedis%2FRedis%20%E5%93%88%E5%B8%8C%E6%A7%BD.md)
  * [Redis 的同步机制](docs%2FRedis%2FRedis%20%E7%9A%84%E5%90%8C%E6%AD%A5%E6%9C%BA%E5%88%B6.md)
  * [redis中hash扩容过程](docs%2FRedis%2Fredis%E4%B8%ADhash%E6%89%A9%E5%AE%B9%E8%BF%87%E7%A8%8B.md)
  * [redis主从复制怎么实现](docs%2FRedis%2Fredis%E4%B8%BB%E4%BB%8E%E5%A4%8D%E5%88%B6%E6%80%8E%E4%B9%88%E5%AE%9E%E7%8E%B0.md)
  * [redis如何实现延时队列](docs%2FRedis%2Fredis%E5%A6%82%E4%BD%95%E5%AE%9E%E7%8E%B0%E5%BB%B6%E6%97%B6%E9%98%9F%E5%88%97.md)
  * [Redis布隆过滤器](docs%2FRedis%2FRedis%E5%B8%83%E9%9A%86%E8%BF%87%E6%BB%A4%E5%99%A8.md)
  * [redis热点问题怎么解决](docs%2FRedis%2Fredis%E7%83%AD%E7%82%B9%E9%97%AE%E9%A2%98%E6%80%8E%E4%B9%88%E8%A7%A3%E5%86%B3.md)
  * [redis的五大数据类型实现原理](docs%2FRedis%2Fredis%E7%9A%84%E4%BA%94%E5%A4%A7%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B%E5%AE%9E%E7%8E%B0%E5%8E%9F%E7%90%86.md)
  * [redis缓存为什么要延时双删](docs%2FRedis%2Fredis%E7%BC%93%E5%AD%98%E4%B8%BA%E4%BB%80%E4%B9%88%E8%A6%81%E5%BB%B6%E6%97%B6%E5%8F%8C%E5%88%A0.md)
  * [Redlock（redis分布式锁）原理分析](docs%2FRedis%2FRedlock%EF%BC%88redis%E5%88%86%E5%B8%83%E5%BC%8F%E9%94%81%EF%BC%89%E5%8E%9F%E7%90%86%E5%88%86%E6%9E%90.md)
  * [Sentinel集群选举机制](docs%2FRedis%2FSentinel%E9%9B%86%E7%BE%A4%E9%80%89%E4%B8%BE%E6%9C%BA%E5%88%B6.md)
  * [String数据结构sds](docs%2FRedis%2FString%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84sds.md)
  * [事务及Lua脚本操作](docs%2FRedis%2F%E4%BA%8B%E5%8A%A1%E5%8F%8ALua%E8%84%9A%E6%9C%AC%E6%93%8D%E4%BD%9C.md)
  * [假设Redis 的 master 节点宕机了，你会怎么进行数据恢复？](docs%2FRedis%2F%E5%81%87%E8%AE%BERedis%20%E7%9A%84%20master%20%E8%8A%82%E7%82%B9%E5%AE%95%E6%9C%BA%E4%BA%86%EF%BC%8C%E4%BD%A0%E4%BC%9A%E6%80%8E%E4%B9%88%E8%BF%9B%E8%A1%8C%E6%95%B0%E6%8D%AE%E6%81%A2%E5%A4%8D%EF%BC%9F.md)
  * [关于影响Redis性能的几点因素](docs%2FRedis%2F%E5%85%B3%E4%BA%8E%E5%BD%B1%E5%93%8DRedis%E6%80%A7%E8%83%BD%E7%9A%84%E5%87%A0%E7%82%B9%E5%9B%A0%E7%B4%A0.md)
  * [内存淘汰策略和过期删除策略](docs%2FRedis%2F%E5%86%85%E5%AD%98%E6%B7%98%E6%B1%B0%E7%AD%96%E7%95%A5%E5%92%8C%E8%BF%87%E6%9C%9F%E5%88%A0%E9%99%A4%E7%AD%96%E7%95%A5.md)
  * [如何处理网络延迟和网络异常](docs%2FRedis%2F%E5%A6%82%E4%BD%95%E5%A4%84%E7%90%86%E7%BD%91%E7%BB%9C%E5%BB%B6%E8%BF%9F%E5%92%8C%E7%BD%91%E7%BB%9C%E5%BC%82%E5%B8%B8.md)
  * [持久化策略RDB和AOF](docs%2FRedis%2F%E6%8C%81%E4%B9%85%E5%8C%96%E7%AD%96%E7%95%A5RDB%E5%92%8CAOF.md)
  * [数据结构 Stream](docs%2FRedis%2F%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84%C2%A0Stream.md)
  * [缓存穿透、击穿、雪崩、预热、更新、降级](docs%2FRedis%2F%E7%BC%93%E5%AD%98%E7%A9%BF%E9%80%8F%E3%80%81%E5%87%BB%E7%A9%BF%E3%80%81%E9%9B%AA%E5%B4%A9%E3%80%81%E9%A2%84%E7%83%AD%E3%80%81%E6%9B%B4%E6%96%B0%E3%80%81%E9%99%8D%E7%BA%A7.md)
* Theory
  * [Ctrl C发生了什么](docs%2FTheory%2FCtrl%20C%E5%8F%91%E7%94%9F%E4%BA%86%E4%BB%80%E4%B9%88.md)
  * [dns是怎么解析的](docs%2FTheory%2Fdns%E6%98%AF%E6%80%8E%E4%B9%88%E8%A7%A3%E6%9E%90%E7%9A%84.md)
  * [epoll怎么解决io效率问题](docs%2FTheory%2Fepoll%E6%80%8E%E4%B9%88%E8%A7%A3%E5%86%B3io%E6%95%88%E7%8E%87%E9%97%AE%E9%A2%98.md)
  * [hash冲突](docs%2FTheory%2Fhash%E5%86%B2%E7%AA%81.md)
  * [hash函数](docs%2FTheory%2Fhash%E5%87%BD%E6%95%B0.md)
  * [kill底层发生了什么](docs%2FTheory%2Fkill%E5%BA%95%E5%B1%82%E5%8F%91%E7%94%9F%E4%BA%86%E4%BB%80%E4%B9%88.md)
  * [Linux中的零拷贝技术](docs%2FTheory%2FLinux%E4%B8%AD%E7%9A%84%E9%9B%B6%E6%8B%B7%E8%B4%9D%E6%8A%80%E6%9C%AF.md)
  * [linux文件的权限](docs%2FTheory%2Flinux%E6%96%87%E4%BB%B6%E7%9A%84%E6%9D%83%E9%99%90.md)
  * [Linux系统态与用户态](docs%2FTheory%2FLinux%E7%B3%BB%E7%BB%9F%E6%80%81%E4%B8%8E%E7%94%A8%E6%88%B7%E6%80%81.md)
  * [LRU 算法及其实现方式](docs%2FTheory%2FLRU%20%E7%AE%97%E6%B3%95%E5%8F%8A%E5%85%B6%E5%AE%9E%E7%8E%B0%E6%96%B9%E5%BC%8F.md)
  * [Protobuf 的底层](docs%2FTheory%2FProtobuf%20%E7%9A%84%E5%BA%95%E5%B1%82.md)
  * [raft算法](docs%2FTheory%2Fraft%E7%AE%97%E6%B3%95.md)
  * [rpc实现原理](docs%2FTheory%2Frpc%E5%AE%9E%E7%8E%B0%E5%8E%9F%E7%90%86.md)
  * [socket 中 select 与 epoll](docs%2FTheory%2Fsocket%20%E4%B8%AD%20select%20%E4%B8%8E%20epoll.md)
  * [共享内存](docs%2FTheory%2F%E5%85%B1%E4%BA%AB%E5%86%85%E5%AD%98.md)
  * [内核态和用户态](docs%2FTheory%2F%E5%86%85%E6%A0%B8%E6%80%81%E5%92%8C%E7%94%A8%E6%88%B7%E6%80%81.md)
  * [分布式id算法](docs%2FTheory%2F%E5%88%86%E5%B8%83%E5%BC%8Fid%E7%AE%97%E6%B3%95.md)
  * [堆和栈访问效率哪个更高](docs%2FTheory%2F%E5%A0%86%E5%92%8C%E6%A0%88%E8%AE%BF%E9%97%AE%E6%95%88%E7%8E%87%E5%93%AA%E4%B8%AA%E6%9B%B4%E9%AB%98.md)
  * [如何设计一个哈希表](docs%2FTheory%2F%E5%A6%82%E4%BD%95%E8%AE%BE%E8%AE%A1%E4%B8%80%E4%B8%AA%E5%93%88%E5%B8%8C%E8%A1%A8.md)
  * [孤儿进程和僵尸进程以及僵死进程的解决方案](docs%2FTheory%2F%E5%AD%A4%E5%84%BF%E8%BF%9B%E7%A8%8B%E5%92%8C%E5%83%B5%E5%B0%B8%E8%BF%9B%E7%A8%8B%E4%BB%A5%E5%8F%8A%E5%83%B5%E6%AD%BB%E8%BF%9B%E7%A8%8B%E7%9A%84%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md)
  * [布式系统的一致性模型（CAP 定理和 PAXOS）](docs%2FTheory%2F%E5%B8%83%E5%BC%8F%E7%B3%BB%E7%BB%9F%E7%9A%84%E4%B8%80%E8%87%B4%E6%80%A7%E6%A8%A1%E5%9E%8B%EF%BC%88CAP%20%E5%AE%9A%E7%90%86%E5%92%8C%20PAXOS%EF%BC%89.md)
  * [常见的乐观锁实现方式有几种](docs%2FTheory%2F%E5%B8%B8%E8%A7%81%E7%9A%84%E4%B9%90%E8%A7%82%E9%94%81%E5%AE%9E%E7%8E%B0%E6%96%B9%E5%BC%8F%E6%9C%89%E5%87%A0%E7%A7%8D.md)
  * [常见的排序算法](docs%2FTheory%2F%E5%B8%B8%E8%A7%81%E7%9A%84%E6%8E%92%E5%BA%8F%E7%AE%97%E6%B3%95.md)
  * [指针和引用的区别](docs%2FTheory%2F%E6%8C%87%E9%92%88%E5%92%8C%E5%BC%95%E7%94%A8%E7%9A%84%E5%8C%BA%E5%88%AB.md)
  * [操作系统中的中断](docs%2FTheory%2F%E6%93%8D%E4%BD%9C%E7%B3%BB%E7%BB%9F%E4%B8%AD%E7%9A%84%E4%B8%AD%E6%96%AD.md)
  * [时间和空间复杂度](docs%2FTheory%2F%E6%97%B6%E9%97%B4%E5%92%8C%E7%A9%BA%E9%97%B4%E5%A4%8D%E6%9D%82%E5%BA%A6.md)
  * [死锁概念，死锁产生的四个必要条件，如何避免和预防死锁](docs%2FTheory%2F%E6%AD%BB%E9%94%81%E6%A6%82%E5%BF%B5%EF%BC%8C%E6%AD%BB%E9%94%81%E4%BA%A7%E7%94%9F%E7%9A%84%E5%9B%9B%E4%B8%AA%E5%BF%85%E8%A6%81%E6%9D%A1%E4%BB%B6%EF%BC%8C%E5%A6%82%E4%BD%95%E9%81%BF%E5%85%8D%E5%92%8C%E9%A2%84%E9%98%B2%E6%AD%BB%E9%94%81.md)
  * [物理内存、虚拟内存和共享内存](docs%2FTheory%2F%E7%89%A9%E7%90%86%E5%86%85%E5%AD%98%E3%80%81%E8%99%9A%E6%8B%9F%E5%86%85%E5%AD%98%E5%92%8C%E5%85%B1%E4%BA%AB%E5%86%85%E5%AD%98.md)
  * [线程间有哪些通信方式](docs%2FTheory%2F%E7%BA%BF%E7%A8%8B%E9%97%B4%E6%9C%89%E5%93%AA%E4%BA%9B%E9%80%9A%E4%BF%A1%E6%96%B9%E5%BC%8F.md)
  * [网络io模型](docs%2FTheory%2F%E7%BD%91%E7%BB%9Cio%E6%A8%A1%E5%9E%8B.md)
  * [自旋锁](docs%2FTheory%2F%E8%87%AA%E6%97%8B%E9%94%81.md)
  * [进程和线程之间有什么区别](docs%2FTheory%2F%E8%BF%9B%E7%A8%8B%E5%92%8C%E7%BA%BF%E7%A8%8B%E4%B9%8B%E9%97%B4%E6%9C%89%E4%BB%80%E4%B9%88%E5%8C%BA%E5%88%AB.md)
  * [进程和线程的同步方式](docs%2FTheory%2F%E8%BF%9B%E7%A8%8B%E5%92%8C%E7%BA%BF%E7%A8%8B%E7%9A%84%E5%90%8C%E6%AD%A5%E6%96%B9%E5%BC%8F.md)
  * [进程间通信方式](docs%2FTheory%2F%E8%BF%9B%E7%A8%8B%E9%97%B4%E9%80%9A%E4%BF%A1%E6%96%B9%E5%BC%8F.md)
  * [通过分析系统，定位服务器问题](docs%2FTheory%2F%E9%80%9A%E8%BF%87%E5%88%86%E6%9E%90%E7%B3%BB%E7%BB%9F%EF%BC%8C%E5%AE%9A%E4%BD%8D%E6%9C%8D%E5%8A%A1%E5%99%A8%E9%97%AE%E9%A2%98.md)
  * [逻辑地址和物理地址的转化](docs%2FTheory%2F%E9%80%BB%E8%BE%91%E5%9C%B0%E5%9D%80%E5%92%8C%E7%89%A9%E7%90%86%E5%9C%B0%E5%9D%80%E7%9A%84%E8%BD%AC%E5%8C%96.md)
* Algorithm
  * [二叉树层次遍历](docs%2FAlgorithm%2F%E4%BA%8C%E5%8F%89%E6%A0%91%E5%B1%82%E6%AC%A1%E9%81%8D%E5%8E%86.md)
  * [反转合并有序链表](docs%2FAlgorithm%2F%E5%8F%8D%E8%BD%AC%E5%90%88%E5%B9%B6%E6%9C%89%E5%BA%8F%E9%93%BE%E8%A1%A8.md)
  * [找出数组中最小的k个数](docs%2FAlgorithm%2F%E6%89%BE%E5%87%BA%E6%95%B0%E7%BB%84%E4%B8%AD%E6%9C%80%E5%B0%8F%E7%9A%84k%E4%B8%AA%E6%95%B0.md)
  * [接雨水](docs%2FAlgorithm%2F%E6%8E%A5%E9%9B%A8%E6%B0%B4.md)
  * [树的遍历](docs%2FAlgorithm%2F%E6%A0%91%E7%9A%84%E9%81%8D%E5%8E%86.md)
* Case
  * [10亿个数中找到最大的一个数以及最大的K个数](docs%2FCase%2F10%E4%BA%BF%E4%B8%AA%E6%95%B0%E4%B8%AD%E6%89%BE%E5%88%B0%E6%9C%80%E5%A4%A7%E7%9A%84%E4%B8%80%E4%B8%AA%E6%95%B0%E4%BB%A5%E5%8F%8A%E6%9C%80%E5%A4%A7%E7%9A%84K%E4%B8%AA%E6%95%B0.md)
  * [ES监听binlog](docs%2FCase%2FES%E7%9B%91%E5%90%ACbinlog.md)
  * [假如明天是活动高峰？QPS 预计会翻10倍，你要怎么做？](docs%2FCase%2F%E5%81%87%E5%A6%82%E6%98%8E%E5%A4%A9%E6%98%AF%E6%B4%BB%E5%8A%A8%E9%AB%98%E5%B3%B0%EF%BC%9FQPS%20%E9%A2%84%E8%AE%A1%E4%BC%9A%E7%BF%BB10%E5%80%8D%EF%BC%8C%E4%BD%A0%E8%A6%81%E6%80%8E%E4%B9%88%E5%81%9A%EF%BC%9F.md)
  * [判断链表是否有回环](docs%2FCase%2F%E5%88%A4%E6%96%AD%E9%93%BE%E8%A1%A8%E6%98%AF%E5%90%A6%E6%9C%89%E5%9B%9E%E7%8E%AF.md)
  * [实时输出最近一个小时内访问频率最高的10个IP](docs%2FCase%2F%E5%AE%9E%E6%97%B6%E8%BE%93%E5%87%BA%E6%9C%80%E8%BF%91%E4%B8%80%E4%B8%AA%E5%B0%8F%E6%97%B6%E5%86%85%E8%AE%BF%E9%97%AE%E9%A2%91%E7%8E%87%E6%9C%80%E9%AB%98%E7%9A%8410%E4%B8%AAIP.md)
  * [实现秒杀功能](docs%2FCase%2F%E5%AE%9E%E7%8E%B0%E7%A7%92%E6%9D%80%E5%8A%9F%E8%83%BD.md)
  * [翻转字符串](docs%2FCase%2F%E7%BF%BB%E8%BD%AC%E5%AD%97%E7%AC%A6%E4%B8%B2.md)
  * [评论系统](docs%2FCase%2F%E8%AF%84%E8%AE%BA%E7%B3%BB%E7%BB%9F.md)

  
这些文档旨在帮助系统化理解这些关键技术概念，为编程和架构设计提供坚实的理论基础。