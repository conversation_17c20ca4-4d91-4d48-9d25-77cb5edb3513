Redis 中判断 Key 是否存在的原理依赖于 Redis 的底层数据结构和哈希表的实现。当你使用 `EXISTS` 命令时，Redis 会在其内部的数据结构中查找相应的 key。具体原理如下：

### 1. 数据结构
Redis 使用**字典**（哈希表）来存储键值对。每个 key 都映射到 Redis 的全局哈希表中，哈希表的 key 是字符串，值可以是各种数据类型（如字符串、列表、集合等）。

### 2. 哈希表查找
当执行 `EXISTS` 命令时，Redis 首先在哈希表中查找该 key：

- 如果哈希表中有该 key，说明 key 存在，Redis 返回 `1`。
- 如果哈希表中没有找到该 key，说明 key 不存在，Redis 返回 `0`。

由于哈希表的查找时间复杂度是 **O(1)**，即无论哈希表中的数据量有多大，查找某个 key 都是非常快速的。Redis 内部采用了**渐进式 rehashing**（重新散列），确保哈希表操作在大多数情况下都能保持高效。

### 3. 过期机制的影响
值得注意的是，Redis 的键值对可以设置过期时间。如果一个 key 设置了过期时间并且已经过期，那么即使它还在哈希表中，但在查找时 Redis 会自动判断它是否已经过期：

- 如果 key 已经过期，Redis 会先将其删除，然后返回 `0`，表示该 key 不存在。
- 如果 key 尚未过期，Redis 会继续返回 `1`，表示 key 仍然存在。

### 总结
Redis 的 `EXISTS` 命令依赖哈希表的数据结构和查找机制，在绝大多数情况下，查找 key 的效率都非常高（O(1) 复杂度）。同时，Redis 的过期机制确保了过期的 key 不会被误判为存在。

