在 Redis 中，哈希表（Hash）的底层实现使用的是字典（dict），其核心是哈希表结构。当 Redis 中的哈希表需要存储更多的键值对时，可能会触发哈希表的扩容过程。扩容过程的目的是为了保持哈希表的操作效率（例如查找、插入、删除等操作的时间复杂度）接近 O(1)。

### Redis 哈希表的扩容过程

Redis 的哈希表扩容过程主要分为以下几个步骤：

1. **判断是否需要扩容**：
   Redis 在插入新元素时会判断当前哈希表的负载因子（Load Factor）来决定是否需要扩容。负载因子是哈希表中已使用的槽（slot）数量与哈希表总大小的比值。当负载因子超过某个阈值时（通常是 1），Redis 会触发扩容。

2. **分配新的哈希表**：
   Redis 会先分配一个新的哈希表 `ht[1]`，其大小通常是现有哈希表 `ht[0]` 的两倍。此时，新旧哈希表会同时存在。

3. **数据渐进式迁移**：
   Redis 采用“渐进式 rehashing”（rehash）的方式将旧哈希表中的数据迁移到新哈希表。具体来说，并不会一次性将所有数据迁移完毕，而是逐步进行：
   - 在每次对哈希表进行插入、删除、查询等操作时，Redis 会顺便将旧哈希表中的一部分数据（通常是一小部分槽位的数据）迁移到新哈希表。
   - 通过这种方式，扩容过程不会造成单次操作的长时间阻塞，从而提高了系统的响应能力。

4. **更新哈希表引用**：
   当所有数据都迁移到新哈希表后，Redis 会将 `ht[1]` 变为 `ht[0]`，并清空原来的 `ht[0]`，从而完成哈希表的扩容。

### 具体扩容策略

- **惰性迁移**：数据迁移是渐进式的，只有在对哈希表进行操作时才会发生。这样做的目的是为了避免一次性迁移造成的系统性能下降。
  
- **避免主键竞争**：在渐进式 rehashing 过程中，新旧哈希表会同时存在。因此，在执行插入、删除、查询操作时，Redis 会先在新哈希表中查找，如果未找到，再到旧哈希表中查找。这样可以确保扩容过程中数据的一致性。

- **扩容与缩容**：Redis 不仅会在需要时对哈希表进行扩容，当哈希表中的键值对数量减少时（例如被删除），Redis 也会根据一定的条件触发哈希表的缩容操作，释放多余的内存资源。

### 总结

Redis 通过渐进式 rehashing 实现了哈希表的扩容，确保了在扩容过程中系统的高效性和低延迟响应。通过这种方式，Redis 能够在处理大量数据时依然保持高性能，避免了扩容过程中可能出现的性能瓶颈。