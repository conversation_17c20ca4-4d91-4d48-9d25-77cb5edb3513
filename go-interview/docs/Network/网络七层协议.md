网络七层协议模型，也称为 OSI（Open Systems Interconnection）模型，是一个用于理解和设计网络通信系统的标准模型。这个模型将网络通信过程划分为七个不同的层次，每一层都有其特定的功能和协议。七层协议模型的各层如下：

1. **物理层 (Physical Layer)**：
   - 负责传输原始的比特流，涉及电气信号、光纤、无线电波等硬件接口和传输介质。
   - 示例协议和标准：IEEE 802.3 (以太网)、IEEE 802.11 (无线局域网) 等。

2. **数据链路层 (Data Link Layer)**：
   - 负责在物理层提供的传输介质上建立、维护和断开连接，处理帧的传输，错误检测和纠正。
   - 示例协议：以太网协议 (Ethernet)、PPP (Point-to-Point Protocol) 等。

3. **网络层 (Network Layer)**：
   - 负责数据包的路由选择和转发，处理逻辑地址（如 IP 地址）和网络层的协议。
   - 示例协议：IP (Internet Protocol)、ICMP (Internet Control Message Protocol) 等。

4. **传输层 (Transport Layer)**：
   - 负责在主机间提供端到端的通信，确保数据的完整性和顺序，处理流量控制和错误恢复。
   - 示例协议：TCP (Transmission Control Protocol)、UDP (User Datagram Protocol) 等。

5. **会话层 (Session Layer)**：
   - 负责管理和控制会话（即数据交换的连接），确保数据交换过程的同步和对话管理。
   - 示例协议：NetBIOS、RPC (Remote Procedure Call) 等。

6. **表示层 (Presentation Layer)**：
   - 负责数据的格式化、加密和解密，确保不同系统间的数据能正确表示和理解。
   - 示例协议：MIME (Multipurpose Internet Mail Extensions)、SSL/TLS (Secure Sockets Layer/Transport Layer Security) 等。

7. **应用层 (Application Layer)**：
   - 负责与用户应用程序进行交互，提供应用服务和接口，处理应用层协议。
   - 示例协议：HTTP (Hypertext Transfer Protocol)、FTP (File Transfer Protocol)、SMTP (Simple Mail Transfer Protocol) 等。

### TCP 和 UDP 在网络协议中的层次

- **TCP (Transmission Control Protocol)**：
  - TCP 位于 **传输层**。它提供了可靠的、面向连接的通信服务，确保数据包按顺序到达，并进行错误检测和重传。TCP 通过建立连接、流量控制和错误恢复机制来保证数据的完整性和可靠性。

- **UDP (User Datagram Protocol)**：
  - UDP 也位于 **传输层**。它提供了无连接的通信服务，虽然速度较快，但不保证数据包的顺序和完整性。UDP 适用于对实时性要求较高或容错能力较强的应用，如视频流和在线游戏。