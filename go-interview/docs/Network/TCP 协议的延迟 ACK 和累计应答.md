### 延迟 ACK (Delayed Acknowledgment)

**定义**：
延迟 ACK 是一种在 TCP 协议中使用的优化技术，它允许接收方在收到数据包后不立即发送 ACK 确认应答，而是延迟一段时间，等待可能会有其他数据到达，以减少网络中的 ACK 包数量。

**工作原理**：
1. 当接收方收到数据包时，它会暂时不发送 ACK 确认应答。
2. 接收方会在一个小的时间窗口（通常是几十毫秒）内等待，看是否会收到更多的数据包。
3. 如果在这个时间窗口内收到了更多的数据包，接收方会将这些数据包的 ACK 一起发送，从而减少了 ACK 包的数量。
4. 如果在等待时间内没有收到更多的数据包，接收方会发送一个确认应答。

**优点**：
- 减少了网络中的 ACK 包数量，从而降低了网络负载。
- 改善了 TCP 的传输效率，尤其是在高延迟网络中。

**缺点**：
- 延迟 ACK 可能会导致增加了传输延迟，因为接收方可能会延迟发送 ACK。
- 在某些高负载情况下，可能会出现 ACK 延迟过长的问题，影响性能。

### 累计应答 (Cumulative Acknowledgment)

**定义**：
累计应答是 TCP 协议中一种确认机制，它允许接收方通过一个 ACK 确认应答来确认所有已接收的数据包，即使这些数据包不是最后一个接收到的数据包。

**工作原理**：
1. 当接收方收到数据包时，它会将 ACK 确认应答发送给发送方，确认所有从序列号 1 到当前收到的数据包的最后一个序列号的数据包都已成功接收。
2. 发送方根据接收到的 ACK 确认应答来更新其发送窗口，知道哪些数据已经被成功接收。

**优点**：
- 简化了接收方的确认逻辑，减少了需要处理的 ACK 数量。
- 改善了 TCP 的吞吐量和性能，因为发送方可以根据累计应答来推测网络状态。

**缺点**：
- 如果接收方丢失了一个数据包或 ACK 包，则所有后续的数据包的确认也会受到影响。
- 可能会导致不必要的数据重传，因为接收方可能会丢失数据包的 ACK，从而要求重新传输所有未确认的数据包。