### **2. TCP 与 UDP 的区别**

TCP（Transmission Control Protocol）和 UDP（User Datagram Protocol）是两种常见的传输层协议，它们在功能和应用场景上有显著区别：

| 特性               | TCP                                   | UDP                                   |
|--------------------|---------------------------------------|---------------------------------------|
| **连接方式**       | 面向连接，需要建立连接（三次握手）    | 无连接，不需要建立连接                |
| **可靠性**         | 提供可靠传输，保证数据不丢失、不重复 | 不保证可靠传输，可能丢包或乱序        |
| **传输方式**       | 面向字节流，数据按顺序传输            | 面向报文，数据以独立的报文形式传输    |
| **速度**           | 较慢（需要确认和重传机制）            | 较快（无确认和重传机制）              |
| **开销**           | 较大（需要维护连接状态）              | 较小（无连接状态）                    |
| **应用场景**       | 适用于可靠性要求高的场景，如文件传输  | 适用于实时性要求高的场景，如视频直播  |

#### **应用场景**
- **TCP**：HTTP/HTTPS、FTP、SMTP、POP3 等。
- **UDP**：DNS、DHCP、视频直播、在线游戏等。

---

### **3. 三次握手和四次挥手**

#### **三次握手（建立连接）**
TCP 使用三次握手来建立可靠的连接，确保双方都能正常发送和接收数据。

1. **第一次握手**（客户端 → 服务端）：  
   客户端发送一个 SYN（同步序列号）报文，表示请求建立连接。

2. **第二次握手**（服务端 → 客户端）：  
   服务端收到 SYN 后，回复一个 SYN + ACK（确认）报文，表示同意连接。

3. **第三次握手**（客户端 → 服务端）：  
   客户端收到 SYN + ACK 后，发送一个 ACK 报文，表示确认连接。

**连接建立后，客户端和服务端可以开始通信。**

#### **四次挥手（断开连接）**
TCP 使用四次挥手来断开连接，确保双方都能正常关闭连接。

1. **第一次挥手**（客户端 → 服务端）：  
   客户端发送一个 FIN（结束）报文，表示不再发送数据。

2. **第二次挥手**（服务端 → 客户端）：  
   服务端收到 FIN 后，回复一个 ACK 报文，表示确认收到。

3. **第三次挥手**（服务端 → 客户端）：  
   服务端发送一个 FIN 报文，表示不再发送数据。

4. **第四次挥手**（客户端 → 服务端）：  
   客户端收到 FIN 后，回复一个 ACK 报文，表示确认收到。

**连接断开后，双方的资源被释放。**

---

### **面试中的常见问题**
1. **为什么三次握手不能减少为两次？**
   - 如果只有两次握手，客户端无法确认服务端是否已准备好接收数据，可能导致数据丢失。

2. **为什么断开连接需要四次挥手？**
   - 因为 TCP 是全双工通信，双方需要分别关闭发送和接收通道。

3. **TCP 如何保证可靠性？**
   - 通过序列号、确认机制、超时重传、流量控制和拥塞控制等机制。

通过理解 TCP 和 UDP 的区别，以及三次握手和四次挥手的过程，可以更好地回答网络协议相关的面试问题。