在MySQL中，联合索引是按照索引中字段的顺序依次存储的。在你提到的联合索引 `(name, age)` 中，`name` 字段在前，`age` 字段在后。这意味着在查询时，MySQL 会优先使用 `name` 字段来过滤数据，然后再使用 `age` 字段进一步筛选。

### 查询条件
- `name LIKE 'c%'`
- `age = 10`

### 索引的使用情况
1. **`name` 字段**：
   - 查询条件是 `name LIKE 'c%'`。由于`LIKE 'c%'`有一个前缀匹配的条件（即没有使用通配符`%`开头），MySQL可以使用索引中的`name`字段进行范围扫描（range scan）。这个扫描会定位到所有以字母“c”开头的`name`字段。

2. **`age` 字段**：
   - 因为`name`字段的前缀匹配已经使用了索引，并且`age`是联合索引中的第二个字段，MySQL接着会在`name`匹配到的结果集中使用`age = 10`的条件进行进一步的过滤。
   - 因此，在联合索引 `(name, age)` 上，MySQL可以有效地利用两个字段的索引来加速查询。

### 索引使用过程
- **第一步**：MySQL首先使用`name`字段的前缀匹配来缩小范围。这会选出所有以“c”开头的`name`字段的记录。
- **第二步**：在范围缩小后的结果集里，MySQL会使用`age = 10`的条件进行精准匹配，进一步过滤出符合条件的记录。

### 总结
对于查询条件 `name LIKE 'c%' AND age = 10`，MySQL会使用联合索引 `(name, age)`，首先根据`name`字段进行前缀匹配，然后根据`age`字段进行精准匹配，从而提高查询的效率。