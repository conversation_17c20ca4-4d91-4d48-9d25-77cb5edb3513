在分表分库后，每个表仍然有百万级别的数据，查询速度很慢的问题，可以通过以下几个方面进行优化：

### 1. **优化索引**
   - **建立合适的索引**：确定查询条件的字段，并在这些字段上建立索引。例如，通常对于 `WHERE` 子句中常用的字段、排序字段、分组字段，创建合适的索引会显著提高查询效率。
   - **覆盖索引**：通过覆盖索引来避免回表查询，即索引已经包含了查询所需的所有列，从而减少 I/O 操作。
   - **复合索引**：根据查询的条件顺序，创建多列复合索引。例如，在同时按 `user_id` 和 `timestamp` 查询时，可以创建 `(user_id, timestamp)` 的复合索引。

### 2. **分区表（Partitioning）**
   如果单个表过大，可以对每个表进行分区管理：
   - **Range 分区**：根据时间范围或者其他递增的字段（如 ID）进行表的划分，查询时会定位到具体的分区，从而缩小数据扫描的范围。
   - **Hash 分区**：对于访问分布不均衡的情况，可以使用 Hash 分区将数据均匀分布到多个分区中，减少单个分区的查询压力。

### 3. **查询优化**
   - **减少扫描行数**：尽量减少返回的字段数量，避免使用 `SELECT *`，仅选择需要的列。
   - **避免使用 `OR` 或非索引列**：如果 `OR` 出现在查询条件中，它会导致索引失效，可以通过 `UNION ALL` 分开查询。还可以避免在 `WHERE` 子句中使用非索引字段。
   - **Limit 和分页**：在查询大数据量时，使用分页技术，但要注意优化大偏移量时的性能，可以结合主键 ID 进行优化。
   
### 4. **查询缓存**
   - **应用缓存**：使用 Redis 或 Memcached 对常用的查询结果进行缓存，减少数据库的查询压力。对于一些热点查询数据，可以将结果存储在缓存中，直接返回缓存结果。
   - **数据库查询缓存**：某些数据库（如 MySQL）提供了查询缓存功能，但需根据实际情况选择，缓存命中率不高时可能会导致性能问题。

### 5. **读写分离**
   - **读写分离**：将查询操作分配到只读的从库上执行，通过读写分离减轻主库的查询压力。在分库的基础上，应用程序可以自动选择从库来执行查询，从而加速查询。

### 6. **数据归档**
   对历史数据进行归档，将不经常访问的旧数据存放到归档库中，只保留最新、最常用的数据在主库中，从而减少主库的数据量和查询时间。

### 7. **水平切分（Sharding）**
   - **二次分库分表**：如果经过初次分表分库后，每个表的数据量依然很大，可以考虑在已有分表分库的基础上，进一步进行更细粒度的水平切分。例如，基于用户 ID 或其他业务字段进一步分表。
   - **查询路由优化**：分库分表后，查询需要选择正确的表或库。可以通过中间件（如 MyCat、ShardingSphere）实现智能路由，尽量减少不必要的查询范围。

### 8. **批量查询优化**
   如果需要进行大批量查询，尤其是关联多个表时，可以通过减少表的关联，优化查询语句：
   - **避免复杂 Join**：对于大表 Join 操作，尽量避免复杂的 Join 操作，可以通过先查询一个表，再在应用层进行数据的关联。

### 9. **数据压缩和存储引擎优化**
   - **使用更高效的存储引擎**：选择适合业务需求的存储引擎，如 MySQL 中 InnoDB 支持更好的并发处理能力和索引优化机制。
   - **压缩数据**：通过对数据列进行压缩，减少存储占用，进而提高查询效率。

### 10. **并行查询**
   在分库分表的情况下，可以通过并行查询提高查询效率。应用程序可以同时向多个分库发出查询请求，并行处理查询结果，减少整体查询时间。

---

### 示例：优化百万级别数据查询的 Golang Demo
以 MySQL 为例，假设你有一个 `users` 表，数据量较大，查询最近的用户登录记录。

#### 1. 查询优化：
可以先创建索引，然后通过分页和复合查询减少扫描的行数。

```sql
-- 创建索引
CREATE INDEX idx_user_login_time ON users(user_id, login_time);

-- 查询最近登录的100个用户
SELECT user_id, login_time FROM users WHERE login_time > NOW() - INTERVAL 1 DAY ORDER BY login_time DESC LIMIT 100;
```

#### 2. Golang 查询优化代码：

```go
package main

import (
	"database/sql"
	"fmt"
	"log"
	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 连接数据库
	db, err := sql.Open("mysql", "user:password@tcp(localhost:3306)/dbname")
	if err != nil {
		log.Fatal(err)
	}
	defer db.Close()

	// 查询最近登录的用户
	rows, err := db.Query("SELECT user_id, login_time FROM users WHERE login_time > NOW() - INTERVAL 1 DAY ORDER BY login_time DESC LIMIT 100")
	if err != nil {
		log.Fatal(err)
	}
	defer rows.Close()

	// 处理查询结果
	for rows.Next() {
		var userID int
		var loginTime string
		if err := rows.Scan(&userID, &loginTime); err != nil {
			log.Fatal(err)
		}
		fmt.Printf("User ID: %d, Login Time: %s\n", userID, loginTime)
	}

	// 检查是否有查询错误
	if err := rows.Err(); err != nil {
		log.Fatal(err)
	}
}
```

### 总结
针对分库分表后每个表仍然有百万级别数据查询速度慢的问题，可以通过索引优化、分区表、缓存、读写分离、水平切分等多种方法来加速查询，确保系统在海量数据下仍具有较好的查询性能。