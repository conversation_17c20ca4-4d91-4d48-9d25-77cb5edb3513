虽然索引能够极大提升查询效率，但并非所有情况下都适合建立索引。以下是哪些情况下适合建立索引，哪些情况不适合的分析：

### 适合建立索引的情况

1. **频繁出现在查询条件的列**
   如果某个列经常用于 `WHERE` 子句中的查询条件、过滤条件，建立索引可以显著提升查询性能。

   **示例**：
   ```sql
   SELECT * FROM users WHERE age = 30;
   ```
   如果 `age` 列经常出现在 `WHERE` 子句中，应该为该列建立索引。

2. **频繁用于排序 (`ORDER BY`) 的列**
   如果某个列经常被用来进行排序操作，建立索引可以加快排序速度。

   **示例**：
   ```sql
   SELECT * FROM users ORDER BY created_at DESC;
   ```
   为 `created_at` 列建立索引可以提高排序效率。

3. **经常用于连接（JOIN）的列**
   如果两个表之间的某些列经常进行 `JOIN` 操作，应该为这些连接列建立索引，以加快连接速度。

   **示例**：
   ```sql
   SELECT * FROM orders 
   JOIN users ON orders.user_id = users.id;
   ```
   建议为 `orders.user_id` 和 `users.id` 建立索引。

4. **唯一性要求的列**
   如果某个列的值必须唯一，可以通过 `UNIQUE` 索引来保证这一约束，同时提高查询速度。

   **示例**：
   ```sql
   SELECT * FROM users WHERE email = '<EMAIL>';
   ```
   为 `email` 列建立唯一索引既能加速查询，又能保证邮箱的唯一性。

5. **高选择性列**
   高选择性列是指列中的不同值的数量较多。对于这样的列，建立索引可以有效提高查询效率，因为索引可以快速定位所需的记录。

   **示例**：
   ```sql
   SELECT * FROM users WHERE ssn = '***********';
   ```
   对于像社会保障号这样的列，值的重复率低，适合建立索引。

6. **组合查询条件**
   对于经常涉及多列查询条件的情况，可以建立复合索引（多列索引），这样可以在多列上提高查询性能。

   **示例**：
   ```sql
   SELECT * FROM users WHERE first_name = 'John' AND last_name = 'Doe';
   ```
   可以为 `(first_name, last_name)` 建立复合索引。

### 不适合建立索引的情况

1. **低选择性列**
   低选择性列是指列中的不同值很少，大多数行的值相同。对于这样的列，建立索引不会带来显著的性能提升，反而会增加索引的维护成本。

   **示例**：
   ```sql
   SELECT * FROM users WHERE gender = 'M';
   ```
   如果 `gender` 列中只有 `M` 和 `F` 两个值，索引可能不太有用。

2. **频繁更新的列**
   对于那些经常被 `UPDATE` 操作修改的列，建立索引可能带来额外的维护开销。因为每次更新索引列时，数据库都需要更新索引，增加了写操作的开销。

   **示例**：
   ```sql
   UPDATE users SET age = age + 1 WHERE id = 100;
   ```
   如果 `age` 列经常被更新，建立索引可能不合适。

3. **非常小的表**
   对于非常小的表，数据量较少，查询时全表扫描的性能几乎等同于通过索引查询的性能。在这种情况下，索引的维护成本反而可能高于它带来的性能提升。

4. **批量插入或删除操作频繁**
   对于大量 `INSERT` 或 `DELETE` 操作的表，索引的存在会导致每次插入或删除操作时都需要同步维护索引，从而增加了写入的开销。

   **示例**：
   ```sql
   INSERT INTO users (id, name) VALUES (1, 'John');
   ```
   如果表数据经常批量插入或删除，频繁维护索引会降低插入性能。

5. **模糊查询开头使用 `%` 的列**
   当使用模糊查询，并且查询条件以 `%` 开头时，索引通常会失效，因此为此类列建立索引没有太大意义。

   **示例**：
   ```sql
   SELECT * FROM users WHERE name LIKE '%ohn';
   ```
   如果查询条件中包含 `%` 号在前，索引不会生效。

6. **宽列（大文本、长字符串）**
   对于非常宽的列，例如存储大文本 (`TEXT`) 或长字符串 (`VARCHAR`) 的列，建立索引可能会消耗大量存储空间，而且查询时的性能提升不明显。

   **示例**：
   ```sql
   SELECT * FROM articles WHERE content LIKE '%keyword%';
   ```
   为 `content` 这种大文本字段建立索引可能得不偿失。

7. **单个查询中很少被使用的列**
   如果某列很少用于查询或者只在极少情况下被用作查询条件，建立索引的收益不大，反而会增加数据库的存储和维护成本。

### 总结
- **适合建索引的列**：经常被查询、排序、连接、作为主键或唯一性列、选择性高的列。
- **不适合建索引的列**：低选择性、频繁更新的列，模糊查询使用 `%` 在前的列，大文本字段，以及很少被查询的列。

选择是否建立索引需要综合考虑查询需求、性能和维护开销。