[介绍]
1. 自我介绍
2. 你之前做的项目中，你主要参与的工作是什么？项目难点是什么？你是如何解决的？
3. 问最熟悉哪一个项目，学到了什么
4. 怎么处理团队协作的问题
5. 对于那些不合群或者不愿意参与团队工作的队友，怎么引导他们？
6. 对于自己的发展有什么期望
7. 你有哪些优点和缺点
8. 你还投了哪些公司？如果发了offer选哪个？为什么？
[Golang]
1. 怎么保证线程安全
2. go channel的底层实现了解吗
3. goroutine什么情况下会阻塞
4. map slice底层实现
5. 穿参数组和传参slice有什么区别？传参slice会有什么问题吗？
6. go内存逃逸分析
7. go的内存分配器
8. 两个goroutine奇偶交替打印1-100
9. gin路由树
10. 异步任务怎么做的？并行串行？消息通知？
11. 在并行任务中，如果存在依赖关系怎么保证消息通知
12. 为什么会想到goroutine加channel的方式实现消息通知
13. 如果goroutine之间传递信息，除了channel还可以用什么？
14. 常见的并发模型有七种，CSP并发模型，以及GPM调度
15. Golang垃圾回收(GC)
16. 你认为golang中接口的作用是什么，有什么好处，底层实现细节
[Mysql]
1. Mysql索引为什么采用B+树
2. 主键为什么选择自增，分布式系统怎么办
3. 事务回滚的原理
4. 事务什么场景下会失效
5. 快照读和当前读的区别
6. 介绍一下MySQL的锁都有什么？了解意向锁吗
7. MySQL bin log有几种格式？
8. bin log和redo log的区别
9. MySQL底层是如何完成千万级数据的快速查找?
10. 线上有一个 SQL 在跑，发现这个 SQL 的执行性能不好，从你的角度来说，你觉得应该怎么优化？
11. 对于给定的 SQL 语句，表放在 JOIN 的左边和右边有区别吗，区别在哪？
12. 有那些锁是自动加的
13. 你是怎样检测死锁的？
14. 事务中的一致性是什么意思。
15. MySQL InnoDB MVCC 机制的原理及实现
16. 一条SQL的执行过程,优化器主要优化的地方
17. 如果表中有上亿条数据怎么优化
18. 什么是事务，事务的四大特性
19. 索引有哪些类型？
20. 你知道偷库吗？宕库呢？怎么解决？
21. ACID的I怎么实现的？
22. 事务什么时候会发生死锁，检测死锁的方式
23. 聚簇索引和非聚簇索引有什么区别？
24. mysql的搜索引擎有哪些，底层实现数据结构，有什么区别？
[操作系统]
1. IO多路复用
2. 一个进程在fork()的时候会复制什么信息
3. Linux的进程管理和内存管理了解吗（不了解）
4. 两个进程之间想互相发送信息应该怎么实现（进程之间的通信方式）？通过网络协议不可以吗？
5. 进程和线程的区别
6. 一个支持高并发的服务会用怎样的进程线程模型
7. 物理内存和虚拟内存的区别
8. 进程的调度方式
9. 进程切换过程中，操作系统做了啥
10. 线程死循环会导致所在进程（单线程进程和多线程进程）出现什么问题，有什么影响？出现假死现象（一定会出现假死吗？）CPU会飙升吗？
11. 线程崩溃会导致进程崩溃吗？一定会导致进程崩溃吗？
12. 线程崩溃之后会使用什么方式通知进程呢？
13. TCP、UDP可以绑定相同的端口吗？多个TCP进程可以绑定同一个端口吗？
14. 同步IO和异步IO介绍一下？
15. 怎么设计一个好的系统和架构？
16. 死锁如何解决，如何查找死锁位置 ？  
[kubernetes]
1. Pod内容器共享了哪些namespace?（IPC,NET,UTS,MNT）
2. kubernetes多集群管理有什么设计？有什么优缺点吗？你觉得哪种方式更好
3. CRD是怎么设计的
4. 看过kubernetes源码吗？
5. kubernetes调度器有什么问题？
6. 你觉得应该怎么保证高实时性调度还有安全调度
7. kubernetes有什么设计局限性吗？
[网络]
1. get和post有啥区别
2. 计算机网络角度说一下输入域名呈现数据的一个过程
3. DNS如何将域名变为IP
4. 三次握手双方通信需要确定哪些字段？
5. UDP 和 TCP 之间的区别，你有了解过吗？
6. 网关有什么作用？
7. http和https的区别
8. TCP四次挥手，为什么需要第四次ack？没有会怎么样？
9. 介绍一下time\_wait是一个什么状态，为什么需要这个状态，有什么作用？
10. time\_wait状态会带来什么副作用吗？
[智力]
1. 两个人玩取石子，每次可以拿1-3个，必胜策略
2. 给同学进入教室和出教室的时间，求教室中的最大在线人数
4. 赛马问题，最快几场
5. 海盗分金
6. 爬台阶问题
7. 一共20个球，甲乙两人依次拿球，每次最少拿1个球，最多拿5个球，若甲先拿 ，最后一个拿完球的人获胜，请问甲在后面怎么拿球才能获胜？
8. 两个空水壶，一个5L,一个6L的水壶，怎么取出3L的水？
[手撕]
1. 删除链表的倒数第n个节点（LC19）
2. 重排链表提问能用空间复杂度为0(1)的算法吗?反问:有什么不足再次提问:对大模型有什么了解吗
3. 合并两个有序链表
4. 字符串之实现 Sunday 匹配
5. 给一个数组找中位数，说一下你的思路以及你设计的算法时间空间复杂度
6. 整数反转
7. 两个栈模拟队列
8. 全排列、链表反向输出
9. 给定一个数组，如何实现队列
10. 数组旋转90度
11. 二叉树后序非递归遍历
12. 寻找第K大
13. 删除链表重复的元素。
14. 算法题1:“Aa213” => 1 输入一个字符串 字符串中有大小写字符和数字，输出字符串中出现次数最多的字符的数量，如果没有重复的字符的话返回0
15. 算法题2:给出一个数值95342551返回其中的最大递减值 他的递减值有95 953 53 42 51 所以要输出953
[数据结构]
1. 常见的哈希冲突有哪些?
2. 说一下雪花id/uuid的实现逻辑？Redis获取唯一id如何操作？
3. CAS 明明有几个判断，它是怎么把它封装成一个原子操作的呢？
4. 发生死锁怎么解决？
5. 讲一下快排 还有它的时间复杂度
6. 红黑树
7. 红黑数和B+树的区别可以总结一下吗
[分布式]
1. 分布式算法介绍
2. 分布式共识算法(Raft、Paxos)
3. CAP理论, BASE理论
4. 分布式介绍
5. 一致性哈希
6. 两阶段提交、三阶段提交
7. 延时队列的实现
[Kafka]
1. 讲一下kafka内部实现
2. Kafka 是怎么实现消息分区的？Kafka 如何保证消息不重复消费？
[RrocketMQ]
1. 讲一下RocketMQ内部实现
2. RocketMQ如何防止消息丢失
3. 是否能说RocketMQ不会丢消息?
4. TTL+死信队列是怎么实现延迟队列的
5. 多个消费者怎么保证消息不被重复消费
[Redis]
1. Redis分布式锁是怎么做的？Redis lua脚本和分布式锁有什么关系？
2. Redis哪里用到了多线程
3. Redis基本数据类型
4. Redis数据结构的底层实现
5. 如何保证缓存和数据库一致性
6. Redis中内存淘汰算法
7. Redis主从复制原理
8. Redis里面的哈希数据结构
9. AOF和RDB有什么区别
10. 假设Redis有一亿个Key，有10万个Key是固定开头的，怎样把他们快速找出来？
11. Redis为什么是原子性的？是怎么保证原子性的？
[场景]
1. 如果客户打电话说服务很慢，或者某个服务有问题，你怎么定位？
2. 如果一个项目上线了，但是出问题了，怎么处理？
3. 怎么看待技术和业务？
4. 职业规划，对什么方向感兴趣？一些个人问题等
5. 如果有个需求客户让你一周交付，但是实际的工程排期可能需要2-3周，你如何解决？