LeetCode 42 “接雨水”（Trapping Rain Water）是一个经典的算法问题，要求计算在一个表示高度的柱状图中，能够接住多少雨水。下面是解决这个问题的一种有效方法，使用 Golang 实现。

### 问题描述

给定一个整数数组 `height`，其中 `height[i]` 表示柱子的高度。找出在这些柱子之间可以接住的雨水总量。

### 解法

一种高效的解决方案是使用双指针法，时间复杂度为 O(n)，空间复杂度为 O(1)。

**双指针法**：
1. 初始化两个指针 `left` 和 `right`，分别指向数组的开始和结束位置。
2. 初始化两个变量 `leftMax` 和 `rightMax` 来存储当前左右指针所经过的最高柱子的高度。
3. 计算左右指针之间接住的雨水：
   - 如果 `height[left]` 小于 `height[right]`，处理左边的情况：
     - 如果 `height[left]` 大于或等于 `leftMax`，更新 `leftMax`。
     - 否则，计算当前的雨水量并累加到结果中。
     - 移动左指针向右。
   - 否则，处理右边的情况：
     - 如果 `height[right]` 大于或等于 `rightMax`，更新 `rightMax`。
     - 否则，计算当前的雨水量并累加到结果中。
     - 移动右指针向左。

### Golang 实现

```go
package main

import "fmt"

// trap calculates the amount of rainwater trapped.
func trap(height []int) int {
    if len(height) == 0 {
        return 0
    }

    left, right := 0, len(height) - 1
    leftMax, rightMax := height[left], height[right]
    waterTrapped := 0

    for left <= right {
        if height[left] < height[right] {
            if height[left] >= leftMax {
                leftMax = height[left]
            } else {
                waterTrapped += leftMax - height[left]
            }
            left++
        } else {
            if height[right] >= rightMax {
                rightMax = height[right]
            } else {
                waterTrapped += rightMax - height[right]
            }
            right--
        }
    }

    return waterTrapped
}

func main() {
    height := []int{0,1,0,2,1,0,1,3,2,1,2,1}
    result := trap(height)
    fmt.Printf("Trapped rainwater: %d\n", result)
}
```

### 解释

- **初始化**：`left` 和 `right` 分别指向数组的开始和结束位置，`leftMax` 和 `rightMax` 存储当前的最大高度。
- **计算雨水量**：通过比较 `height[left]` 和 `height[right]` 来决定处理哪一侧。更新 `leftMax` 和 `rightMax` 并计算当前的雨水量。
- **移动指针**：根据比较结果，移动左指针或右指针。

这个算法通过一次遍历就能计算出总的雨水量，效率较高。