#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
常见基础算法实现集合
包含排序算法、搜索算法、数据结构等常见面试题算法
作者: 面试准备
日期: 2024
"""

import random
from typing import List, Any
import time


class SortingAlgorithms:
    """排序算法集合"""
    
    @staticmethod
    def bubble_sort(arr: List[int]) -> List[int]:
        """
        冒泡排序
        时间复杂度: O(n²)
        空间复杂度: O(1)
        稳定性: 稳定
        
        原理: 重复遍历数组，比较相邻元素，如果顺序错误就交换
        """
        n = len(arr)
        arr = arr.copy()  # 不修改原数组
        
        for i in range(n):
            # 标记本轮是否有交换，优化算法
            swapped = False
            
            # 每轮将最大元素"冒泡"到末尾
            for j in range(0, n - i - 1):
                if arr[j] > arr[j + 1]:
                    arr[j], arr[j + 1] = arr[j + 1], arr[j]
                    swapped = True
            
            # 如果没有交换，说明已经有序
            if not swapped:
                break
                
        return arr
    
    @staticmethod
    def quick_sort(arr: List[int]) -> List[int]:
        """
        快速排序
        时间复杂度: 平均O(n log n), 最坏O(n²)
        空间复杂度: O(log n)
        稳定性: 不稳定
        
        原理: 分治法，选择基准元素，将数组分为小于和大于基准的两部分
        """
        if len(arr) <= 1:
            return arr
        
        # 选择中间元素作为基准（避免最坏情况）
        pivot = arr[len(arr) // 2]
        
        # 分割数组
        left = [x for x in arr if x < pivot]
        middle = [x for x in arr if x == pivot]
        right = [x for x in arr if x > pivot]
        
        # 递归排序并合并
        return SortingAlgorithms.quick_sort(left) + middle + SortingAlgorithms.quick_sort(right)
    
    @staticmethod
    def merge_sort(arr: List[int]) -> List[int]:
        """
        归并排序
        时间复杂度: O(n log n)
        空间复杂度: O(n)
        稳定性: 稳定
        
        原理: 分治法，将数组分成两半，递归排序后合并
        """
        if len(arr) <= 1:
            return arr
        
        # 分割数组
        mid = len(arr) // 2
        left = SortingAlgorithms.merge_sort(arr[:mid])
        right = SortingAlgorithms.merge_sort(arr[mid:])
        
        # 合并两个有序数组
        return SortingAlgorithms._merge(left, right)
    
    @staticmethod
    def _merge(left: List[int], right: List[int]) -> List[int]:
        """合并两个有序数组"""
        result = []
        i = j = 0
        
        # 比较两个数组的元素，将较小的加入结果
        while i < len(left) and j < len(right):
            if left[i] <= right[j]:
                result.append(left[i])
                i += 1
            else:
                result.append(right[j])
                j += 1
        
        # 添加剩余元素
        result.extend(left[i:])
        result.extend(right[j:])
        
        return result
    
    @staticmethod
    def heap_sort(arr: List[int]) -> List[int]:
        """
        堆排序
        时间复杂度: O(n log n)
        空间复杂度: O(1)
        稳定性: 不稳定
        
        原理: 构建最大堆，然后依次取出堆顶元素
        """
        arr = arr.copy()
        n = len(arr)
        
        # 构建最大堆
        for i in range(n // 2 - 1, -1, -1):
            SortingAlgorithms._heapify(arr, n, i)
        
        # 依次取出堆顶元素
        for i in range(n - 1, 0, -1):
            arr[0], arr[i] = arr[i], arr[0]  # 将堆顶移到末尾
            SortingAlgorithms._heapify(arr, i, 0)  # 重新调整堆
        
        return arr
    
    @staticmethod
    def _heapify(arr: List[int], n: int, i: int):
        """调整堆结构"""
        largest = i  # 假设父节点最大
        left = 2 * i + 1  # 左子节点
        right = 2 * i + 2  # 右子节点
        
        # 找出最大值的索引
        if left < n and arr[left] > arr[largest]:
            largest = left
        
        if right < n and arr[right] > arr[largest]:
            largest = right
        
        # 如果最大值不是父节点，交换并继续调整
        if largest != i:
            arr[i], arr[largest] = arr[largest], arr[i]
            SortingAlgorithms._heapify(arr, n, largest)


class SearchAlgorithms:
    """搜索算法集合"""
    
    @staticmethod
    def linear_search(arr: List[int], target: int) -> int:
        """
        线性搜索
        时间复杂度: O(n)
        空间复杂度: O(1)
        
        原理: 逐个检查数组元素
        """
        for i, value in enumerate(arr):
            if value == target:
                return i
        return -1
    
    @staticmethod
    def binary_search(arr: List[int], target: int) -> int:
        """
        二分搜索
        时间复杂度: O(log n)
        空间复杂度: O(1)
        前提: 数组必须有序
        
        原理: 每次比较中间元素，缩小搜索范围
        """
        left, right = 0, len(arr) - 1
        
        while left <= right:
            mid = (left + right) // 2
            
            if arr[mid] == target:
                return mid
            elif arr[mid] < target:
                left = mid + 1
            else:
                right = mid - 1
        
        return -1
    
    @staticmethod
    def binary_search_recursive(arr: List[int], target: int, left: int = 0, right: int = None) -> int:
        """
        递归版本的二分搜索
        """
        if right is None:
            right = len(arr) - 1
        
        if left > right:
            return -1
        
        mid = (left + right) // 2
        
        if arr[mid] == target:
            return mid
        elif arr[mid] < target:
            return SearchAlgorithms.binary_search_recursive(arr, target, mid + 1, right)
        else:
            return SearchAlgorithms.binary_search_recursive(arr, target, left, mid - 1)


class GraphAlgorithms:
    """图算法集合"""

    @staticmethod
    def dfs(graph: dict, start: str, visited: set = None) -> List[str]:
        """
        深度优先搜索
        时间复杂度: O(V + E)
        空间复杂度: O(V)

        原理: 尽可能深地搜索图的分支
        """
        if visited is None:
            visited = set()

        result = []
        if start not in visited:
            visited.add(start)
            result.append(start)

            for neighbor in graph.get(start, []):
                result.extend(GraphAlgorithms.dfs(graph, neighbor, visited))

        return result

    @staticmethod
    def bfs(graph: dict, start: str) -> List[str]:
        """
        广度优先搜索
        时间复杂度: O(V + E)
        空间复杂度: O(V)

        原理: 逐层搜索图的节点
        """
        from collections import deque

        visited = set()
        queue = deque([start])
        result = []

        while queue:
            node = queue.popleft()
            if node not in visited:
                visited.add(node)
                result.append(node)

                for neighbor in graph.get(node, []):
                    if neighbor not in visited:
                        queue.append(neighbor)

        return result

    @staticmethod
    def dijkstra(graph: dict, start: str) -> dict:
        """
        Dijkstra最短路径算法
        时间复杂度: O((V + E) log V)
        空间复杂度: O(V)

        原理: 贪心算法，每次选择距离最短的未访问节点
        """
        import heapq

        distances = {node: float('inf') for node in graph}
        distances[start] = 0
        pq = [(0, start)]
        visited = set()

        while pq:
            current_distance, current_node = heapq.heappop(pq)

            if current_node in visited:
                continue

            visited.add(current_node)

            for neighbor, weight in graph.get(current_node, []):
                distance = current_distance + weight

                if distance < distances[neighbor]:
                    distances[neighbor] = distance
                    heapq.heappush(pq, (distance, neighbor))

        return distances


class StringAlgorithms:
    """字符串算法集合"""

    @staticmethod
    def kmp_search(text: str, pattern: str) -> List[int]:
        """
        KMP字符串匹配算法
        时间复杂度: O(n + m)
        空间复杂度: O(m)

        原理: 利用已匹配的信息，避免重复比较
        """
        def build_lps(pattern):
            """构建最长前缀后缀数组"""
            lps = [0] * len(pattern)
            length = 0
            i = 1

            while i < len(pattern):
                if pattern[i] == pattern[length]:
                    length += 1
                    lps[i] = length
                    i += 1
                else:
                    if length != 0:
                        length = lps[length - 1]
                    else:
                        lps[i] = 0
                        i += 1
            return lps

        if not pattern:
            return []

        lps = build_lps(pattern)
        result = []
        i = j = 0

        while i < len(text):
            if text[i] == pattern[j]:
                i += 1
                j += 1

            if j == len(pattern):
                result.append(i - j)
                j = lps[j - 1]
            elif i < len(text) and text[i] != pattern[j]:
                if j != 0:
                    j = lps[j - 1]
                else:
                    i += 1

        return result

    @staticmethod
    def longest_common_subsequence(text1: str, text2: str) -> int:
        """
        最长公共子序列
        时间复杂度: O(m * n)
        空间复杂度: O(m * n)

        原理: 动态规划，比较字符是否相等
        """
        m, n = len(text1), len(text2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if text1[i-1] == text2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])

        return dp[m][n]


class MathAlgorithms:
    """数学算法集合"""

    @staticmethod
    def gcd(a: int, b: int) -> int:
        """
        最大公约数 - 欧几里得算法
        时间复杂度: O(log min(a, b))
        空间复杂度: O(1)

        原理: gcd(a, b) = gcd(b, a % b)
        """
        while b:
            a, b = b, a % b
        return a

    @staticmethod
    def lcm(a: int, b: int) -> int:
        """
        最小公倍数
        原理: lcm(a, b) = a * b / gcd(a, b)
        """
        return abs(a * b) // MathAlgorithms.gcd(a, b)

    @staticmethod
    def is_prime(n: int) -> bool:
        """
        素数判断
        时间复杂度: O(√n)
        空间复杂度: O(1)

        原理: 只需检查到√n
        """
        if n < 2:
            return False
        if n == 2:
            return True
        if n % 2 == 0:
            return False

        for i in range(3, int(n**0.5) + 1, 2):
            if n % i == 0:
                return False
        return True

    @staticmethod
    def sieve_of_eratosthenes(n: int) -> List[int]:
        """
        埃拉托斯特尼筛法 - 找出所有小于n的素数
        时间复杂度: O(n log log n)
        空间复杂度: O(n)

        原理: 标记合数，剩下的就是素数
        """
        if n < 2:
            return []

        is_prime = [True] * n
        is_prime[0] = is_prime[1] = False

        for i in range(2, int(n**0.5) + 1):
            if is_prime[i]:
                for j in range(i*i, n, i):
                    is_prime[j] = False

        return [i for i in range(n) if is_prime[i]]

    @staticmethod
    def fast_power(base: int, exp: int, mod: int = None) -> int:
        """
        快速幂算法
        时间复杂度: O(log exp)
        空间复杂度: O(1)

        原理: 二进制分解指数
        """
        result = 1
        base = base % mod if mod else base

        while exp > 0:
            if exp % 2 == 1:
                result = (result * base) % mod if mod else result * base
            exp = exp >> 1
            base = (base * base) % mod if mod else base * base

        return result


class DataStructures:
    """基础数据结构实现"""

    class ListNode:
        """链表节点"""
        def __init__(self, val: int = 0, next_node=None):
            self.val = val
            self.next = next_node
    
    class LinkedList:
        """单向链表实现"""
        
        def __init__(self):
            self.head = None
            self.size = 0
        
        def append(self, val: int):
            """在链表末尾添加元素"""
            new_node = DataStructures.ListNode(val)
            
            if not self.head:
                self.head = new_node
            else:
                current = self.head
                while current.next:
                    current = current.next
                current.next = new_node
            
            self.size += 1
        
        def prepend(self, val: int):
            """在链表开头添加元素"""
            new_node = DataStructures.ListNode(val, self.head)
            self.head = new_node
            self.size += 1
        
        def delete(self, val: int) -> bool:
            """删除指定值的节点"""
            if not self.head:
                return False
            
            if self.head.val == val:
                self.head = self.head.next
                self.size -= 1
                return True
            
            current = self.head
            while current.next:
                if current.next.val == val:
                    current.next = current.next.next
                    self.size -= 1
                    return True
                current = current.next
            
            return False
        
        def find(self, val: int) -> bool:
            """查找指定值"""
            current = self.head
            while current:
                if current.val == val:
                    return True
                current = current.next
            return False
        
        def to_list(self) -> List[int]:
            """转换为Python列表"""
            result = []
            current = self.head
            while current:
                result.append(current.val)
                current = current.next
            return result
    
    class Stack:
        """栈实现"""
        
        def __init__(self):
            self.items = []
        
        def push(self, item: Any):
            """入栈"""
            self.items.append(item)
        
        def pop(self) -> Any:
            """出栈"""
            if self.is_empty():
                raise IndexError("Stack is empty")
            return self.items.pop()
        
        def peek(self) -> Any:
            """查看栈顶元素"""
            if self.is_empty():
                raise IndexError("Stack is empty")
            return self.items[-1]
        
        def is_empty(self) -> bool:
            """检查栈是否为空"""
            return len(self.items) == 0
        
        def size(self) -> int:
            """获取栈大小"""
            return len(self.items)
    
    class Queue:
        """队列实现"""
        
        def __init__(self):
            self.items = []
        
        def enqueue(self, item: Any):
            """入队"""
            self.items.append(item)
        
        def dequeue(self) -> Any:
            """出队"""
            if self.is_empty():
                raise IndexError("Queue is empty")
            return self.items.pop(0)
        
        def front(self) -> Any:
            """查看队首元素"""
            if self.is_empty():
                raise IndexError("Queue is empty")
            return self.items[0]
        
        def is_empty(self) -> bool:
            """检查队列是否为空"""
            return len(self.items) == 0
        
        def size(self) -> int:
            """获取队列大小"""
            return len(self.items)


class BacktrackingAlgorithms:
    """回溯算法集合"""

    @staticmethod
    def n_queens(n: int) -> List[List[str]]:
        """
        N皇后问题
        时间复杂度: O(N!)
        空间复杂度: O(N)

        原理: 回溯法，逐行放置皇后，检查冲突
        """
        def is_safe(board, row, col):
            # 检查列
            for i in range(row):
                if board[i][col] == 'Q':
                    return False

            # 检查左上对角线
            for i, j in zip(range(row-1, -1, -1), range(col-1, -1, -1)):
                if board[i][j] == 'Q':
                    return False

            # 检查右上对角线
            for i, j in zip(range(row-1, -1, -1), range(col+1, n)):
                if board[i][j] == 'Q':
                    return False

            return True

        def solve(board, row):
            if row == n:
                return [[''.join(row) for row in board]]

            solutions = []
            for col in range(n):
                if is_safe(board, row, col):
                    board[row][col] = 'Q'
                    solutions.extend(solve(board, row + 1))
                    board[row][col] = '.'

            return solutions

        board = [['.' for _ in range(n)] for _ in range(n)]
        return solve(board, 0)

    @staticmethod
    def generate_parentheses(n: int) -> List[str]:
        """
        生成所有有效的括号组合
        时间复杂度: O(4^n / √n) - 卡特兰数
        空间复杂度: O(4^n / √n)

        原理: 回溯法，确保左括号数量不超过n，右括号数量不超过左括号
        """
        def backtrack(current, left, right):
            if len(current) == 2 * n:
                result.append(current)
                return

            if left < n:
                backtrack(current + '(', left + 1, right)

            if right < left:
                backtrack(current + ')', left, right + 1)

        result = []
        backtrack('', 0, 0)
        return result


class DynamicProgramming:
    """动态规划算法集合"""

    @staticmethod
    def fibonacci(n: int) -> int:
        """
        斐波那契数列 - 动态规划版本
        时间复杂度: O(n)
        空间复杂度: O(1)

        原理: 状态转移方程 f(n) = f(n-1) + f(n-2)
        """
        if n <= 1:
            return n

        a, b = 0, 1
        for _ in range(2, n + 1):
            a, b = b, a + b

        return b

    @staticmethod
    def coin_change(coins: List[int], amount: int) -> int:
        """
        零钱兑换问题
        时间复杂度: O(amount * len(coins))
        空间复杂度: O(amount)

        原理: dp[i] = min(dp[i-coin] + 1) for coin in coins
        """
        dp = [float('inf')] * (amount + 1)
        dp[0] = 0

        for i in range(1, amount + 1):
            for coin in coins:
                if i >= coin:
                    dp[i] = min(dp[i], dp[i - coin] + 1)

        return dp[amount] if dp[amount] != float('inf') else -1

    @staticmethod
    def knapsack_01(weights: List[int], values: List[int], capacity: int) -> int:
        """
        0-1背包问题
        时间复杂度: O(n * capacity)
        空间复杂度: O(capacity)

        原理: 对每个物品选择拿或不拿
        """
        n = len(weights)
        dp = [0] * (capacity + 1)

        for i in range(n):
            for w in range(capacity, weights[i] - 1, -1):
                dp[w] = max(dp[w], dp[w - weights[i]] + values[i])

        return dp[capacity]


class BackendScenarios:
    """后端常见场景算法"""

    @staticmethod
    def rate_limiter_sliding_window(window_size: int, max_requests: int):
        """
        滑动窗口限流器
        应用场景: API限流、防刷

        原理: 维护一个时间窗口，统计窗口内的请求数量
        """
        import time
        from collections import deque

        class SlidingWindowRateLimiter:
            def __init__(self):
                self.requests = deque()

            def is_allowed(self) -> bool:
                now = time.time()

                # 移除过期的请求
                while self.requests and self.requests[0] <= now - window_size:
                    self.requests.popleft()

                # 检查是否超过限制
                if len(self.requests) < max_requests:
                    self.requests.append(now)
                    return True

                return False

        return SlidingWindowRateLimiter()

    @staticmethod
    def consistent_hashing():
        """
        一致性哈希算法
        应用场景: 分布式缓存、负载均衡

        原理: 将服务器和数据都映射到哈希环上，数据存储到顺时针最近的服务器
        """
        import hashlib
        import bisect

        class ConsistentHash:
            def __init__(self, replicas=3):
                self.replicas = replicas
                self.ring = {}
                self.sorted_keys = []

            def _hash(self, key: str) -> int:
                return int(hashlib.md5(key.encode()).hexdigest(), 16)

            def add_server(self, server: str):
                for i in range(self.replicas):
                    key = self._hash(f"{server}:{i}")
                    self.ring[key] = server
                    bisect.insort(self.sorted_keys, key)

            def remove_server(self, server: str):
                for i in range(self.replicas):
                    key = self._hash(f"{server}:{i}")
                    if key in self.ring:
                        del self.ring[key]
                        self.sorted_keys.remove(key)

            def get_server(self, data_key: str) -> str:
                if not self.ring:
                    return None

                key = self._hash(data_key)
                idx = bisect.bisect_right(self.sorted_keys, key)

                if idx == len(self.sorted_keys):
                    idx = 0

                return self.ring[self.sorted_keys[idx]]

        return ConsistentHash()

    @staticmethod
    def lru_cache(capacity: int):
        """
        LRU缓存实现
        应用场景: 内存缓存、页面置换

        原理: 双向链表 + 哈希表，O(1)时间复杂度
        """
        class Node:
            def __init__(self, key=0, value=0):
                self.key = key
                self.value = value
                self.prev = None
                self.next = None

        class LRUCache:
            def __init__(self):
                self.capacity = capacity
                self.cache = {}
                # 创建虚拟头尾节点
                self.head = Node()
                self.tail = Node()
                self.head.next = self.tail
                self.tail.prev = self.head

            def _add_node(self, node):
                """在头部添加节点"""
                node.prev = self.head
                node.next = self.head.next
                self.head.next.prev = node
                self.head.next = node

            def _remove_node(self, node):
                """移除节点"""
                prev_node = node.prev
                next_node = node.next
                prev_node.next = next_node
                next_node.prev = prev_node

            def _move_to_head(self, node):
                """移动节点到头部"""
                self._remove_node(node)
                self._add_node(node)

            def _pop_tail(self):
                """弹出尾部节点"""
                last_node = self.tail.prev
                self._remove_node(last_node)
                return last_node

            def get(self, key: int) -> int:
                node = self.cache.get(key)
                if node:
                    self._move_to_head(node)
                    return node.value
                return -1

            def put(self, key: int, value: int):
                node = self.cache.get(key)

                if node:
                    node.value = value
                    self._move_to_head(node)
                else:
                    new_node = Node(key, value)

                    if len(self.cache) >= self.capacity:
                        tail = self._pop_tail()
                        del self.cache[tail.key]

                    self.cache[key] = new_node
                    self._add_node(new_node)

        return LRUCache()

    @staticmethod
    def bloom_filter(capacity: int, error_rate: float = 0.01):
        """
        布隆过滤器实现
        应用场景: 去重、缓存穿透防护

        原理: 位数组 + 多个哈希函数，允许假阳性但不允许假阴性
        """
        import math
        import hashlib

        class BloomFilter:
            def __init__(self):
                # 计算最优参数
                self.size = int(-capacity * math.log(error_rate) / (math.log(2) ** 2))
                self.hash_count = int(self.size * math.log(2) / capacity)
                self.bit_array = [0] * self.size

            def _hash(self, item: str, seed: int) -> int:
                hash_obj = hashlib.md5(f"{item}{seed}".encode())
                return int(hash_obj.hexdigest(), 16) % self.size

            def add(self, item: str):
                for i in range(self.hash_count):
                    index = self._hash(item, i)
                    self.bit_array[index] = 1

            def contains(self, item: str) -> bool:
                for i in range(self.hash_count):
                    index = self._hash(item, i)
                    if self.bit_array[index] == 0:
                        return False
                return True

        return BloomFilter()

    @staticmethod
    def distributed_lock_redis():
        """
        基于Redis的分布式锁
        应用场景: 分布式系统中的互斥操作

        原理: SET key value NX EX timeout
        """
        import uuid

        class RedisDistributedLock:
            def __init__(self, redis_client, key: str, timeout: int = 10):
                self.redis = redis_client
                self.key = key
                self.timeout = timeout
                self.identifier = str(uuid.uuid4())

            def acquire(self) -> bool:
                """获取锁"""
                # 模拟Redis SET命令: SET key value NX EX timeout
                # 实际使用时需要Redis客户端
                return True  # 简化实现

            def release(self) -> bool:
                """释放锁"""
                # Lua脚本确保原子性，实际使用时执行Lua脚本
                # if redis.call("get", KEYS[1]) == ARGV[1] then
                #     return redis.call("del", KEYS[1])
                # else
                #     return 0
                # end
                return True  # 简化实现

        return RedisDistributedLock

    @staticmethod
    def circuit_breaker(failure_threshold: int = 5, timeout: int = 60):
        """
        断路器模式
        应用场景: 微服务容错、防止雪崩

        原理: 统计失败次数，超过阈值时熔断，定时尝试恢复
        """
        import time
        from enum import Enum

        class CircuitState(Enum):
            CLOSED = "CLOSED"
            OPEN = "OPEN"
            HALF_OPEN = "HALF_OPEN"

        class CircuitBreaker:
            def __init__(self):
                self.failure_count = 0
                self.last_failure_time = None
                self.state = CircuitState.CLOSED

            def call(self, func, *args, **kwargs):
                if self.state == CircuitState.OPEN:
                    if time.time() - self.last_failure_time > timeout:
                        self.state = CircuitState.HALF_OPEN
                    else:
                        raise Exception("Circuit breaker is OPEN")

                try:
                    result = func(*args, **kwargs)
                    self._on_success()
                    return result
                except Exception as e:
                    self._on_failure()
                    raise e

            def _on_success(self):
                self.failure_count = 0
                self.state = CircuitState.CLOSED

            def _on_failure(self):
                self.failure_count += 1
                self.last_failure_time = time.time()

                if self.failure_count >= failure_threshold:
                    self.state = CircuitState.OPEN

        return CircuitBreaker()


def performance_test():
    """性能测试函数"""
    print("=== 算法性能测试 ===")

    # 生成测试数据
    test_sizes = [1000, 5000, 10000]

    for size in test_sizes:
        print(f"\n测试数据大小: {size}")
        test_data = [random.randint(1, 1000) for _ in range(size)]

        # 测试各种排序算法
        algorithms = [
            ("冒泡排序", SortingAlgorithms.bubble_sort),
            ("快速排序", SortingAlgorithms.quick_sort),
            ("归并排序", SortingAlgorithms.merge_sort),
            ("堆排序", SortingAlgorithms.heap_sort),
        ]

        for name, func in algorithms:
            start_time = time.time()
            sorted_data = func(test_data)
            end_time = time.time()

            # 验证排序正确性
            is_correct = sorted_data == sorted(test_data)

            print(f"{name}: {end_time - start_time:.4f}秒 {'✓' if is_correct else '✗'}")


if __name__ == "__main__":
    # 示例使用
    print("=== 基础算法演示 ===")

    # 排序算法演示
    test_array = [64, 34, 25, 12, 22, 11, 90]
    print(f"原数组: {test_array}")

    print(f"冒泡排序: {SortingAlgorithms.bubble_sort(test_array)}")
    print(f"快速排序: {SortingAlgorithms.quick_sort(test_array)}")
    print(f"归并排序: {SortingAlgorithms.merge_sort(test_array)}")
    print(f"堆排序: {SortingAlgorithms.heap_sort(test_array)}")

    # 搜索算法演示
    sorted_array = [11, 12, 22, 25, 34, 64, 90]
    target = 25
    print(f"\n在数组 {sorted_array} 中搜索 {target}:")
    print(f"线性搜索结果: {SearchAlgorithms.linear_search(sorted_array, target)}")
    print(f"二分搜索结果: {SearchAlgorithms.binary_search(sorted_array, target)}")

    # 图算法演示
    print("\n=== 图算法演示 ===")
    graph = {
        'A': ['B', 'C'],
        'B': ['D', 'E'],
        'C': ['F'],
        'D': [],
        'E': ['F'],
        'F': []
    }
    print(f"图结构: {graph}")
    print(f"DFS遍历: {GraphAlgorithms.dfs(graph, 'A')}")
    print(f"BFS遍历: {GraphAlgorithms.bfs(graph, 'A')}")

    # 字符串算法演示
    print("\n=== 字符串算法演示 ===")
    text = "ABABDABACDABABCABCABCABCABC"
    pattern = "ABABCABCABCABC"
    print(f"文本: {text}")
    print(f"模式: {pattern}")
    print(f"KMP搜索结果: {StringAlgorithms.kmp_search(text, pattern)}")

    text1, text2 = "ABCDGH", "AEDFHR"
    print(f"LCS({text1}, {text2}): {StringAlgorithms.longest_common_subsequence(text1, text2)}")

    # 数学算法演示
    print("\n=== 数学算法演示 ===")
    print(f"gcd(48, 18): {MathAlgorithms.gcd(48, 18)}")
    print(f"lcm(12, 18): {MathAlgorithms.lcm(12, 18)}")
    print(f"is_prime(17): {MathAlgorithms.is_prime(17)}")
    print(f"前20个素数: {MathAlgorithms.sieve_of_eratosthenes(20)}")
    print(f"2^10 mod 1000: {MathAlgorithms.fast_power(2, 10, 1000)}")

    # 回溯算法演示
    print("\n=== 回溯算法演示 ===")
    print(f"4皇后问题解的数量: {len(BacktrackingAlgorithms.n_queens(4))}")
    print(f"生成3对括号: {BacktrackingAlgorithms.generate_parentheses(3)}")

    # 动态规划演示
    print("\n=== 动态规划演示 ===")
    print(f"斐波那契数列第10项: {DynamicProgramming.fibonacci(10)}")
    print(f"零钱兑换[1,3,4]组成6: {DynamicProgramming.coin_change([1, 3, 4], 6)}")
    print(f"0-1背包问题: {DynamicProgramming.knapsack_01([2, 1, 3, 2], [12, 10, 20, 15], 5)}")

    # 后端场景演示
    print("\n=== 后端场景算法演示 ===")

    # 限流器演示
    rate_limiter = BackendScenarios.rate_limiter_sliding_window(60, 10)  # 1分钟内最多10个请求
    print(f"限流器测试: {[rate_limiter.is_allowed() for _ in range(12)]}")

    # 一致性哈希演示
    ch = BackendScenarios.consistent_hashing()
    servers = ["server1", "server2", "server3"]
    for server in servers:
        ch.add_server(server)

    data_keys = ["user:1001", "user:1002", "user:1003", "user:1004"]
    print("一致性哈希分布:")
    for key in data_keys:
        server = ch.get_server(key)
        print(f"  {key} -> {server}")

    # LRU缓存演示
    lru = BackendScenarios.lru_cache(3)
    operations = [
        ("put", 1, 1), ("put", 2, 2), ("get", 1),
        ("put", 3, 3), ("get", 2), ("put", 4, 4), ("get", 1)
    ]
    print("LRU缓存操作:")
    for op in operations:
        if op[0] == "put":
            lru.put(op[1], op[2])
            print(f"  put({op[1]}, {op[2]})")
        else:
            result = lru.get(op[1])
            print(f"  get({op[1]}) = {result}")

    # 布隆过滤器演示
    bf = BackendScenarios.bloom_filter(1000, 0.01)
    test_items = ["apple", "banana", "cherry", "date"]
    for item in test_items[:3]:
        bf.add(item)

    print("布隆过滤器测试:")
    for item in test_items:
        exists = bf.contains(item)
        print(f"  {item}: {'可能存在' if exists else '一定不存在'}")

    # 断路器演示
    cb = BackendScenarios.circuit_breaker(3, 5)

    def unreliable_service(should_fail=False):
        if should_fail:
            raise Exception("Service failed")
        return "Success"

    print("断路器测试:")
    # 模拟服务失败
    for i in range(5):
        try:
            result = cb.call(unreliable_service, should_fail=(i < 4))
            print(f"  调用{i+1}: {result}")
        except Exception as e:
            print(f"  调用{i+1}: 失败 - {e}")

    # 数据结构演示
    print("\n=== 数据结构演示 ===")

    # 链表演示
    linked_list = DataStructures.LinkedList()
    for val in [1, 2, 3, 4, 5]:
        linked_list.append(val)
    print(f"链表: {linked_list.to_list()}")

    # 栈演示
    stack = DataStructures.Stack()
    for val in [1, 2, 3]:
        stack.push(val)
    print(f"栈顶元素: {stack.peek()}")
    print(f"出栈: {stack.pop()}")

    # 队列演示
    queue = DataStructures.Queue()
    for val in [1, 2, 3]:
        queue.enqueue(val)
    print(f"队首元素: {queue.front()}")
    print(f"出队: {queue.dequeue()}")

    # 运行性能测试
    performance_test()
