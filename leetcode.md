**每日时间安排 (2小时)：**
*   **前 15 分钟：闪电复习** —— 快速回顾前一天做的题目和总结的“套路”。
*   **中间 90 分钟：专注攻克** —— 严格按照清单，思考并完成当天的题目。卡住超过20分钟就果断看题解，重点是理解思路，然后自己独立实现。
*   **最后 15 分钟：沉淀总结** —— 用自己的话总结今天题目的核心解法，标记出仍有疑问的题目。

---

### **每日攻克清单**

#### **Day 1: 数组与哈希表**
*   **核心思想**: 双指针、滑动窗口、哈希表空间换时间。
*   **题目清单 (LeetCode)**:
    *   `[ ] 1. 两数之和` (哈希表入门)
    *   `[ ] 15. 三数之和` (排序+双指针) **(高频)**
    *   `[ ] 11. 盛最多水的容器` (双指针)
    *   `[ ] 283. 移动零` (双指针基础)
    *   `[ ] 560. 和为 K 的子数组` (前缀和+哈希表) **(高频)**
    *   `[ ] 42. 接雨水` (单调栈/双指针) **(王牌难题)**

---

#### **Day 2: 链表**
*   **核心思想**: 虚拟头节点、快慢指针、递归反转。
*   **题目清单 (LeetCode)**:
    *   `[ ] 206. 反转链表` (迭代/递归) **(必考)**
    *   `[ ] 21. 合并两个有序链表` (必考)
    *   `[ ] 141. 环形链表` (快慢指针)
    *   `[ ] 19. 删除链表的倒数第 N 个结点` (快慢指针)
    *   `[ ] 25. K 个一组翻转链表` (综合性强) **(极高频)**
    *   `[ ] 143. 重排链表` (综合考察链表操作)

---

#### **Day 3: 栈与队列**
*   **核心思想**: 单调栈/队列解决“寻找下一个更大/小元素”问题。
*   **题目清单 (LeetCode)**:
    *   `[ ] 20. 有效的括号` **(必考)**
    *   `[ ] 155. 最小栈` (辅助栈) **(高频)**
    *   `[ ] 739. 每日温度` (单调栈入门)
    *   `[ ] 239. 滑动窗口最大值` (单调队列) **(高频难题)**
    *   `[ ] 232. 用栈实现队列`
    *   `[ ] 84. 柱状图中最大的矩形` (单调栈应用) **(王牌难题)**

---

#### **Day 4: 二叉树 (基础与遍历)**
*   **核心思想**: 深度优先(DFS/递归)与广度优先(BFS/队列)的模板化。
*   **题目清单 (LeetCode)**:
    *   `[ ] 94. 二叉树的中序遍历` (迭代+递归) **(必考)**
    *   `[ ] 144. 二叉树的前序遍历` (同上)
    *   `[ ] 102. 二叉树的层序遍历` (BFS模板) **(必考)**
    *   `[ ] 226. 翻转二叉树` (递归入门)
    *   `[ ] 103. 二叉树的锯齿形层序遍历` (BFS变种)
    *   `[ ] 543. 二叉树的直径` (树形DP入门)

---

#### **Day 5: 二叉树 (构造与搜索)**
*   **核心思想**: 利用递归函数的定义和返回值解决复杂问题，掌握BST特性。
*   **题目清单 (LeetCode)**:
    *   `[ ] 98. 验证二叉搜索树` (BST特性) **(高频)**
    *   `[ ] 105. 从前序与中序遍历序列构造二叉树` **(高频)**
    *   `[ ] 236. 二叉树的最近公共祖先 (LCA)` **(极高频)**
    *   `[ ] 230. 二叉搜索树中第K小的元素` (BST中序遍历)
    *   `[ ] 114. 二叉树展开为链表` (分解问题思想)

---

#### **Day 6: 查找与排序**
*   **核心思想**: 手写快速排序，熟练掌握二分查找的各种变体。
*   **题目清单 (LeetCode)**:
    *   `[ ] 手写快速排序` **(必须掌握)**
    *   `[ ] 215. 数组中的第K个最大元素` (快排思想/堆) **(极高频)**
    *   `[ ] 33. 搜索旋转排序数组` (二分变体) **(高频)**
    *   `[ ] 34. 在排序数组中查找元素的第一个和最后一个位置` (二分变体)
    *   `[ ] 56. 合并区间` (排序应用)

---

#### **Day 7: 回溯与图**
*   **核心思想**: 画出递归树，理解 `path` 和 `result`，搞懂剪枝条件。
*   **题目清单 (LeetCode)**:
    *   `[ ] 46. 全排列` (回溯模板) **(必考)**
    *   `[ ] 78. 子集` (回溯模板)
    *   `[ ] 17. 电话号码的字母组合` **(高频)**
    *   `[ ] 51. N 皇后` (回溯剪枝经典)
    *   `[ ] 200. 岛屿数量` (DFS/BFS入门图论) **(极高频)**

---

#### **Day 8: 动态规划 (基础)**
*   **核心思想**: 五步法：定义DP数组含义 -> 状态转移方程 -> 初始化 -> 遍历顺序 -> 打印DP数组验证。
*   **题目清单 (LeetCode)**:
    *   `[ ] 70. 爬楼梯` (DP入门)
    *   `[ ] 53. 最大子数组和` **(高频)**
    *   `[ ] 198. 打家劫舍` (DP经典)
    *   `[ ] 62. 不同路径` (二维DP)
    *   `[ ] 322. 零钱兑换` (完全背包) **(高频)**
    *   `[ ] 5. 最长回文子串` (DP/中心扩展) **(高频)**

---

#### **Day 9: 动态规划 (进阶)**
*   **核心思想**: 序列DP模型（LCS, LIS），背包问题。
*   **题目清单 (LeetCode)**:
    *   `[ ] 300. 最长递增子序列 (LIS)` **(极高频)**
    *   `[ ] 1143. 最长公共子序列 (LCS)` **(高频)**
    *   `[ ] 72. 编辑距离` (LCS变种) **(高频难题)**
    *   `[ ] 416. 分割等和子集` (0-1背包)

---

#### **Day 10: 字符串、设计与总复习**
*   **核心思想**: 将所有知识融会贯通，准备好必考的设计题，进行模拟面试。
*   **题目清单 (LeetCode)**:
    *   `[ ] 146. LRU 缓存` **(王牌中的王牌，必须白板手写)**
    *   `[ ] 3. 无重复字符的最长子串` (滑动窗口) **(极高频)**
    *   `[ ] 415. 字符串相加` (大数运算)
    *   `[ ] 208. 实现 Trie (前缀树)` (理解思想与应用场景)
    *   `[ ]` **自我模拟面试**：任选一道复杂题目（如 `LRU`, `K个一组翻转链表`），对着白板或IDE，清晰地讲出完整思路、编码、复杂度分析和测试用例。

---

### **面试前的最后心法**

1.  **沟通为王**：拿到题，先说思路，再写代码。清晰的沟通比完美的代码更重要。
2.  **基础扎实**：快速复习计算机网络(TCP/HTTP)、操作系统(进程/线程)的核心概念。
3.  **项目在胸**：准备好1-2个项目的介绍（使用STAR法则）。
4.  **心态制胜**：面试前一晚保证充足睡眠，相信自己10天的努力。


class Node:
    def __init__(self, key=None, val=None):
        self.key = key
        self.val = val
        self.prev = None
        self.next_ = None


class DoubleLinkedList:
    def __init__(self):
        self.dummy_head = Node()
        self.dummy_tail = Node()
        self.dummy_head.next_ = self.dummy_tail
        self.dummy_tail.prev = self.dummy_head
        self.size = 0

    def delete(self, node):
        prev = node.prev
        next_ = node.next_
        prev.next_ = next_
        next_.prev = prev
        self.size -= 1

    def add_tail(self, node):
        prev = self.dummy_tail.prev
        prev.next_ = node
        node.prev = prev
        node.next_ = self.dummy_tail
        self.dummy_tail.prev = node
        self.size += 1

    def remove_head(self):
        if self.size == 0:
            return None
        
        head = self.dummy_head.next_
        self.delete(head)
        return head


class LRUCache:

    def __init__(self, capacity: int):
        self.capacity = capacity
        self.map = dict()
        self.cache = DoubleLinkedList()

    def get(self, key: int) -> int:
        if key not in self.map:
            return -1
        node = self.map[key]
        self.cache.delete(node)
        self.cache.add_tail(node)
        return node.val

    def put(self, key: int, value: int) -> None:
        if key in self.map:
            node = self.map[key]
            node.val = value
            self.cache.delete(node)
            self.cache.add_tail(node)
            return
        
        if len(self.map) == self.capacity:
            del_node = self.cache.remove_head()
            del self.map[del_node.key]

        node = Node(key, value)
        self.map[key] = node
        self.cache.add_tail(node)


# Your LRUCache object will be instantiated and called as such:
# obj = LRUCache(capacity)
# param_1 = obj.get(key)
# obj.put(key,value)